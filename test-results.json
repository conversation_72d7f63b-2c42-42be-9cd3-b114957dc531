[{"endpoint": "v1/admin/management", "method": "GET", "status": "error", "statusCode": 401, "responseTime": 1204, "error": "Request failed with status code 401", "data": "Unauthorized access"}, {"endpoint": "v1/admin/carts", "method": "GET", "status": "error", "statusCode": 404, "responseTime": 287, "error": "Request failed with status code 404", "data": {"message": "Cannot GET:/v1/admin/carts"}}, {"endpoint": "v1/admin/shop/categories", "method": "GET", "status": "error", "statusCode": 404, "responseTime": 284, "error": "Request failed with status code 404", "data": {"message": "Cannot GET:/v1/admin/shop/categories"}}, {"endpoint": "v1/admin/shop/collections", "method": "GET", "status": "error", "statusCode": 404, "responseTime": 288, "error": "Request failed with status code 404", "data": {"message": "Cannot GET:/v1/admin/shop/collections"}}, {"endpoint": "v1/admin/shop/search", "method": "GET", "status": "error", "statusCode": 404, "responseTime": 391, "error": "Request failed with status code 404", "data": {"message": "Cannot GET:/v1/admin/shop/search"}}, {"endpoint": "v1/admin/product-categories", "method": "GET", "status": "error", "statusCode": 401, "responseTime": 294, "error": "Request failed with status code 401", "data": "Unauthorized access"}, {"endpoint": "v1/admin/product-vendor", "method": "GET", "status": "error", "statusCode": 401, "responseTime": 292, "error": "Request failed with status code 401", "data": "Unauthorized access"}, {"endpoint": "v1/admin/product-tag", "method": "GET", "status": "error", "statusCode": 401, "responseTime": 427, "error": "Request failed with status code 401", "data": "Unauthorized access"}, {"endpoint": "v1/admin/product-type", "method": "GET", "status": "error", "statusCode": 401, "responseTime": 307, "error": "Request failed with status code 401", "data": "Unauthorized access"}]