/**
 * Test runner for Admin Shop API endpoints
 * Run with: node test-admin-shop.cjs
 */

const axios = require("axios");

// JWT Token for testing
const TEST_JWT_TOKEN =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiJjNzJhOGJkYi01YTJmLTQzZjItYmEyNy1mNzcwYTBkNDJjYjMiLCJpYXQiOjE3NTUyMjg0Nzd9.uewa4XL6cvE3sRrD7ozolbIFpQ06bb0dc1_-rfQezTs";

// API Base URL
const API_BASE_URL = "https://api.zurno.com";
const ENDPOINT = "v1/admin/shop";

// Configure axios with JWT token
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    Authorization: `Bearer ${TEST_JWT_TOKEN}`,
    "Content-Type": "application/json",
  },
  timeout: 30000, // 30 seconds timeout
});

// Test utility functions
const logTest = (testName, status, details) => {
  const timestamp = new Date().toISOString();
  const emoji = status === "start" ? "🧪" : status === "success" ? "✅" : "❌";
  console.log(`${emoji} [${timestamp}] ${testName}`, details ? details : "");
};

const analyzeResponseStructure = (data) => {
  if (Array.isArray(data)) {
    return {
      type: "array",
      length: data.length,
      firstItemStructure:
        data.length > 0 ? analyzeResponseStructure(data[0]) : null,
    };
  } else if (typeof data === "object" && data !== null) {
    const structure = { type: "object", properties: {} };
    for (const key in data) {
      if (data.hasOwnProperty(key)) {
        structure.properties[key] = {
          type: typeof data[key],
          isNull: data[key] === null,
          isArray: Array.isArray(data[key]),
        };
      }
    }
    return structure;
  } else {
    return { type: typeof data, value: data };
  }
};

// Test functions for each endpoint
const testGetCategories = async () => {
  const testName = "GET Categories";
  logTest(testName, "start");

  const startTime = Date.now();
  try {
    const response = await apiClient.get(`${ENDPOINT}/categories`);
    const responseTime = Date.now() - startTime;

    logTest(testName, "success", {
      statusCode: response.status,
      dataLength: response.data?.data?.length,
      responseTime: `${responseTime}ms`,
    });

    return {
      endpoint: `${ENDPOINT}/categories`,
      method: "GET",
      status: "success",
      statusCode: response.status,
      responseTime,
      data: response.data,
      responseStructure: analyzeResponseStructure(response.data),
    };
  } catch (error) {
    const responseTime = Date.now() - startTime;
    logTest(testName, "error", {
      error: error.message,
      statusCode: error.response?.status,
      responseTime: `${responseTime}ms`,
    });

    return {
      endpoint: `${ENDPOINT}/categories`,
      method: "GET",
      status: "error",
      statusCode: error.response?.status,
      responseTime,
      error: error.message,
      data: error.response?.data,
    };
  }
};

const testGetCollections = async () => {
  const testName = "GET Collections";
  logTest(testName, "start");

  const startTime = Date.now();
  try {
    const response = await apiClient.get(`${ENDPOINT}/collections`);
    const responseTime = Date.now() - startTime;

    logTest(testName, "success", {
      statusCode: response.status,
      dataLength: response.data?.data?.length,
      responseTime: `${responseTime}ms`,
    });

    return {
      endpoint: `${ENDPOINT}/collections`,
      method: "GET",
      status: "success",
      statusCode: response.status,
      responseTime,
      data: response.data,
      responseStructure: analyzeResponseStructure(response.data),
    };
  } catch (error) {
    const responseTime = Date.now() - startTime;
    logTest(testName, "error", {
      error: error.message,
      statusCode: error.response?.status,
      responseTime: `${responseTime}ms`,
    });

    return {
      endpoint: `${ENDPOINT}/collections`,
      method: "GET",
      status: "error",
      statusCode: error.response?.status,
      responseTime,
      error: error.message,
      data: error.response?.data,
    };
  }
};

const testGetCategoryProducts = async (categoryId) => {
  const testName = `GET Category Products (${categoryId})`;
  logTest(testName, "start");

  const startTime = Date.now();
  try {
    const response = await apiClient.get(
      `${ENDPOINT}/categories/${categoryId}/products`,
      {
        params: {
          page: 1,
          perPage: 10,
          sort: "title",
          order: "asc",
        },
      }
    );
    const responseTime = Date.now() - startTime;

    logTest(testName, "success", {
      statusCode: response.status,
      dataLength: response.data?.data?.length,
      responseTime: `${responseTime}ms`,
    });

    return {
      endpoint: `${ENDPOINT}/categories/${categoryId}/products`,
      method: "GET",
      status: "success",
      statusCode: response.status,
      responseTime,
      data: response.data,
      responseStructure: analyzeResponseStructure(response.data),
    };
  } catch (error) {
    const responseTime = Date.now() - startTime;
    logTest(testName, "error", {
      error: error.message,
      statusCode: error.response?.status,
      responseTime: `${responseTime}ms`,
    });

    return {
      endpoint: `${ENDPOINT}/categories/${categoryId}/products`,
      method: "GET",
      status: "error",
      statusCode: error.response?.status,
      responseTime,
      error: error.message,
      data: error.response?.data,
    };
  }
};

const testGetCollectionProducts = async (collectionId) => {
  const testName = `GET Collection Products (${collectionId})`;
  logTest(testName, "start");

  const startTime = Date.now();
  try {
    const response = await apiClient.get(
      `${ENDPOINT}/collections/${collectionId}/products`,
      {
        params: {
          page: 1,
          perPage: 10,
          orderBy: "BEST_SELLING",
          reverse: false,
        },
      }
    );
    const responseTime = Date.now() - startTime;

    logTest(testName, "success", {
      statusCode: response.status,
      productsLength: response.data?.products?.length,
      responseTime: `${responseTime}ms`,
    });

    return {
      endpoint: `${ENDPOINT}/collections/${collectionId}/products`,
      method: "GET",
      status: "success",
      statusCode: response.status,
      responseTime,
      data: response.data,
      responseStructure: analyzeResponseStructure(response.data),
    };
  } catch (error) {
    const responseTime = Date.now() - startTime;
    logTest(testName, "error", {
      error: error.message,
      statusCode: error.response?.status,
      responseTime: `${responseTime}ms`,
    });

    return {
      endpoint: `${ENDPOINT}/collections/${collectionId}/products`,
      method: "GET",
      status: "error",
      statusCode: error.response?.status,
      responseTime,
      error: error.message,
      data: error.response?.data,
    };
  }
};

const testSearchProducts = async (query) => {
  const testName = `Search Products (${query})`;
  logTest(testName, "start");

  const startTime = Date.now();
  try {
    const response = await apiClient.get(`${ENDPOINT}/search`, {
      params: {
        q: query,
        page: 1,
        perPage: 10,
        sortBy: "relevance",
        sortOrder: "desc",
      },
    });
    const responseTime = Date.now() - startTime;

    logTest(testName, "success", {
      statusCode: response.status,
      responseTime: `${responseTime}ms`,
    });

    return {
      endpoint: `${ENDPOINT}/search`,
      method: "GET",
      status: "success",
      statusCode: response.status,
      responseTime,
      data: response.data,
      responseStructure: analyzeResponseStructure(response.data),
    };
  } catch (error) {
    const responseTime = Date.now() - startTime;
    logTest(testName, "error", {
      error: error.message,
      statusCode: error.response?.status,
      responseTime: `${responseTime}ms`,
    });

    return {
      endpoint: `${ENDPOINT}/search`,
      method: "GET",
      status: "error",
      statusCode: error.response?.status,
      responseTime,
      error: error.message,
      data: error.response?.data,
    };
  }
};

// Test working admin endpoints first
const testAdminManagement = async () => {
  const testName = "GET Admin Management";
  logTest(testName, "start");

  const startTime = Date.now();
  try {
    const response = await apiClient.get("v1/admin/management", {
      params: { page: 1, limit: 5 },
    });
    const responseTime = Date.now() - startTime;

    logTest(testName, "success", {
      statusCode: response.status,
      dataLength: response.data?.data?.length,
      responseTime: `${responseTime}ms`,
    });

    return {
      endpoint: "v1/admin/management",
      method: "GET",
      status: "success",
      statusCode: response.status,
      responseTime,
      data: response.data,
      responseStructure: analyzeResponseStructure(response.data),
    };
  } catch (error) {
    const responseTime = Date.now() - startTime;
    logTest(testName, "error", {
      error: error.message,
      statusCode: error.response?.status,
      responseTime: `${responseTime}ms`,
    });

    return {
      endpoint: "v1/admin/management",
      method: "GET",
      status: "error",
      statusCode: error.response?.status,
      responseTime,
      error: error.message,
      data: error.response?.data,
    };
  }
};

const testAdminCarts = async () => {
  const testName = "GET Admin Carts";
  logTest(testName, "start");

  const startTime = Date.now();
  try {
    const response = await apiClient.get("v1/admin/carts", {
      params: { page: 1, limit: 5 },
    });
    const responseTime = Date.now() - startTime;

    logTest(testName, "success", {
      statusCode: response.status,
      dataLength: response.data?.data?.length,
      responseTime: `${responseTime}ms`,
    });

    return {
      endpoint: "v1/admin/carts",
      method: "GET",
      status: "success",
      statusCode: response.status,
      responseTime,
      data: response.data,
      responseStructure: analyzeResponseStructure(response.data),
    };
  } catch (error) {
    const responseTime = Date.now() - startTime;
    logTest(testName, "error", {
      error: error.message,
      statusCode: error.response?.status,
      responseTime: `${responseTime}ms`,
    });

    return {
      endpoint: "v1/admin/carts",
      method: "GET",
      status: "error",
      statusCode: error.response?.status,
      responseTime,
      error: error.message,
      data: error.response?.data,
    };
  }
};

// Test fallback endpoints (existing working endpoints)
const testFallbackCategories = async () => {
  const testName = "GET Fallback Categories (product-categories)";
  logTest(testName, "start");

  const startTime = Date.now();
  try {
    const response = await apiClient.get("v1/admin/product-categories", {
      params: { page: 1, limit: 5 },
    });
    const responseTime = Date.now() - startTime;

    logTest(testName, "success", {
      statusCode: response.status,
      dataLength: response.data?.data?.length,
      responseTime: `${responseTime}ms`,
    });

    return {
      endpoint: "v1/admin/product-categories",
      method: "GET",
      status: "success",
      statusCode: response.status,
      responseTime,
      data: response.data,
      responseStructure: analyzeResponseStructure(response.data),
    };
  } catch (error) {
    const responseTime = Date.now() - startTime;
    logTest(testName, "error", {
      error: error.message,
      statusCode: error.response?.status,
      responseTime: `${responseTime}ms`,
    });

    return {
      endpoint: "v1/admin/product-categories",
      method: "GET",
      status: "error",
      statusCode: error.response?.status,
      responseTime,
      error: error.message,
      data: error.response?.data,
    };
  }
};

const testFallbackVendors = async () => {
  const testName = "GET Fallback Vendors (product-vendor)";
  logTest(testName, "start");

  const startTime = Date.now();
  try {
    const response = await apiClient.get("v1/admin/product-vendor", {
      params: { page: 1, limit: 5 },
    });
    const responseTime = Date.now() - startTime;

    logTest(testName, "success", {
      statusCode: response.status,
      dataLength: response.data?.data?.length,
      responseTime: `${responseTime}ms`,
    });

    return {
      endpoint: "v1/admin/product-vendor",
      method: "GET",
      status: "success",
      statusCode: response.status,
      responseTime,
      data: response.data,
      responseStructure: analyzeResponseStructure(response.data),
    };
  } catch (error) {
    const responseTime = Date.now() - startTime;
    logTest(testName, "error", {
      error: error.message,
      statusCode: error.response?.status,
      responseTime: `${responseTime}ms`,
    });

    return {
      endpoint: "v1/admin/product-vendor",
      method: "GET",
      status: "error",
      statusCode: error.response?.status,
      responseTime,
      error: error.message,
      data: error.response?.data,
    };
  }
};

const testFallbackTags = async () => {
  const testName = "GET Fallback Tags (product-tag)";
  logTest(testName, "start");

  const startTime = Date.now();
  try {
    const response = await apiClient.get("v1/admin/product-tag", {
      params: { page: 1, limit: 5 },
    });
    const responseTime = Date.now() - startTime;

    logTest(testName, "success", {
      statusCode: response.status,
      dataLength: response.data?.data?.length,
      responseTime: `${responseTime}ms`,
    });

    return {
      endpoint: "v1/admin/product-tag",
      method: "GET",
      status: "success",
      statusCode: response.status,
      responseTime,
      data: response.data,
      responseStructure: analyzeResponseStructure(response.data),
    };
  } catch (error) {
    const responseTime = Date.now() - startTime;
    logTest(testName, "error", {
      error: error.message,
      statusCode: error.response?.status,
      responseTime: `${responseTime}ms`,
    });

    return {
      endpoint: "v1/admin/product-tag",
      method: "GET",
      status: "error",
      statusCode: error.response?.status,
      responseTime,
      error: error.message,
      data: error.response?.data,
    };
  }
};

const testFallbackTypes = async () => {
  const testName = "GET Fallback Types (product-type)";
  logTest(testName, "start");

  const startTime = Date.now();
  try {
    const response = await apiClient.get("v1/admin/product-type", {
      params: { page: 1, limit: 5 },
    });
    const responseTime = Date.now() - startTime;

    logTest(testName, "success", {
      statusCode: response.status,
      dataLength: response.data?.data?.length,
      responseTime: `${responseTime}ms`,
    });

    return {
      endpoint: "v1/admin/product-type",
      method: "GET",
      status: "success",
      statusCode: response.status,
      responseTime,
      data: response.data,
      responseStructure: analyzeResponseStructure(response.data),
    };
  } catch (error) {
    const responseTime = Date.now() - startTime;
    logTest(testName, "error", {
      error: error.message,
      statusCode: error.response?.status,
      responseTime: `${responseTime}ms`,
    });

    return {
      endpoint: "v1/admin/product-type",
      method: "GET",
      status: "error",
      statusCode: error.response?.status,
      responseTime,
      error: error.message,
      data: error.response?.data,
    };
  }
};

// Main test runner function
const runAllTests = async () => {
  console.log("🚀 Starting comprehensive Admin Shop API tests...");
  console.log("JWT Token:", TEST_JWT_TOKEN.substring(0, 50) + "...");

  const startTime = Date.now();
  const results = [];

  // First test working endpoints to verify authentication
  console.log("\n🔍 Testing working admin endpoints first...");
  results.push(await testAdminManagement());
  results.push(await testAdminCarts());

  // Then test shop endpoints (expected to fail)
  console.log("\n🛍️ Testing shop endpoints (expected to fail)...");
  results.push(await testGetCategories());
  results.push(await testGetCollections());
  results.push(await testSearchProducts("nail"));

  // Test fallback endpoints (existing working endpoints)
  console.log(
    "\n🔄 Testing fallback endpoints (existing working endpoints)..."
  );
  results.push(await testFallbackCategories());
  results.push(await testFallbackVendors());
  results.push(await testFallbackTags());
  results.push(await testFallbackTypes());

  // Test 4: Get Category Products (if categories exist)
  const categoriesResult = results.find(
    (r) =>
      r.endpoint.includes("/categories") && !r.endpoint.includes("/products")
  );
  if (
    categoriesResult?.status === "success" &&
    categoriesResult.data?.data?.length > 0
  ) {
    const firstCategoryId = categoriesResult.data.data[0].id;
    results.push(await testGetCategoryProducts(firstCategoryId));
  }

  // Test 5: Get Collection Products (if collections exist)
  const collectionsResult = results.find(
    (r) =>
      r.endpoint.includes("/collections") && !r.endpoint.includes("/products")
  );
  if (
    collectionsResult?.status === "success" &&
    collectionsResult.data?.data?.length > 0
  ) {
    const firstCollectionId = collectionsResult.data.data[0].id;
    results.push(await testGetCollectionProducts(firstCollectionId));
  }

  const endTime = Date.now();
  const duration = endTime - startTime;

  const passed = results.filter((r) => r.status === "success").length;
  const failed = results.filter((r) => r.status === "error").length;

  console.log("\n📊 Test Summary:");
  console.log(`Total tests: ${results.length}`);
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`⏱️ Duration: ${duration}ms`);

  // Log detailed results
  console.log("\n📋 Detailed Results:");
  results.forEach((result, index) => {
    const status = result.status === "success" ? "✅" : "❌";
    console.log(
      `${status} ${index + 1}. ${result.method} ${result.endpoint} (${
        result.statusCode || "N/A"
      }) - ${result.responseTime}ms`
    );
    if (result.error) {
      console.log(`   Error: ${result.error}`);
    }
  });

  // Save detailed results to file
  const fs = require("fs");
  fs.writeFileSync("test-results.json", JSON.stringify(results, null, 2));
  console.log("\n💾 Detailed results saved to test-results.json");

  return {
    summary: {
      total: results.length,
      passed,
      failed,
      duration,
    },
    results,
  };
};

// Run the tests
runAllTests()
  .then((result) => {
    console.log("\n🎉 Test completed:", result.summary);
    process.exit(result.summary.failed > 0 ? 1 : 0);
  })
  .catch((error) => {
    console.error("\n💥 Test failed:", error);
    process.exit(1);
  });
