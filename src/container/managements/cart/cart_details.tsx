import React, { FC, Fragment, useEffect, useState } from "react";
import { Bad<PERSON>, Card, Col, Row, Alert } from "react-bootstrap";
import { Link, useParams, useNavigate } from "react-router-dom";
import { LoadingOverlay } from "../../../components/loading/loading-overlay";
import Card<PERSON>eader<PERSON>ithBack from "../../../components/table-title/card-header-with-back";
import {
  useLazyGetCartDetailsQuery,
  useDeleteCartMutation,
} from "../../../services/cart";
import moment from "moment";

import { TCart, TCartSection } from "../../../types/cart";
import Swal from "sweetalert2";
import CartItemManagement from "./cart_item_management";

interface ManagementCartDetailsProps {}

const ManagementCartDetails: FC<ManagementCartDetailsProps> = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [cart, setCart] = useState<TCart | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const [trigger] = useLazyGetCartDetailsQuery();
  const [deleteCart] = useDeleteCartMutation();
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    if (!id) return;

    setIsLoading(true);
    trigger(id)
      .unwrap()
      .then((res: TCart) => {
        setCart(res);
      })
      .catch(() => {
        navigate("/managements-carts");
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, [id, trigger, navigate]);

  const formatDate = (dateString: string) => {
    return moment(dateString).format("MMM DD, YYYY HH:mm:ss");
  };

  const getCustomerName = (cart: TCart) => {
    const { firstName, lastName, fullname } = cart.user;
    if (fullname && fullname.trim()) {
      return fullname;
    }
    return `${firstName} ${lastName}`.trim() || "N/A";
  };

  const getCalculatedItemCount = (cart: TCart) => {
    return cart.itemCount;
  };

  const getCalculatedTotalValue = (cart: TCart) => {
    return (
      cart.totalValue ||
      cart.cartSections.reduce(
        (sum: number, section: TCartSection) => sum + parseFloat(section.total),
        0
      )
    );
  };

  const handleDeleteCart = async () => {
    if (!cart) return;

    const result = await Swal.fire({
      title: "Are you sure?",
      text: "Are you sure you want to delete this cart? This action cannot be undone.",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete it!",
    });

    if (result.isConfirmed) {
      setIsDeleting(true);
      try {
        await deleteCart(cart.id).unwrap();

        await Swal.fire(
          "Deleted!",
          "Cart has been deleted successfully.",
          "success"
        );

        navigate("/managements-carts");
      } catch (error) {
        console.error("Failed to delete cart:", error);
        await Swal.fire(
          "Error!",
          "Failed to delete cart. Please try again.",
          "error"
        );
      } finally {
        setIsDeleting(false);
      }
    }
  };

  if (!cart) {
    return null;
  }

  return (
    <Fragment>
      {isLoading && <LoadingOverlay />}
      <Row>
        <Col xl={12}>
          <Card className="custom-card">
            <Card.Header>
              <CardHeaderWithBack
                title="Cart Details"
                route="/managements-carts"
              />
            </Card.Header>
            <Card.Body>
              {isLoading ? (
                <div
                  className="d-flex justify-content-center align-items-center"
                  style={{ minHeight: "400px" }}
                >
                  <div className="text-center">
                    <div
                      className="spinner-border text-primary mb-3"
                      role="status"
                    >
                      <span className="visually-hidden">Loading...</span>
                    </div>
                    <div className="text-muted">Loading cart details...</div>
                  </div>
                </div>
              ) : cart ? (
                <>
                  {/* Cart Summary */}
                  <Row className="mb-4">
                    <Col md={6}>
                      <Card className="border">
                        <Card.Body>
                          <h6 className="card-title">Cart Information</h6>
                          <div className="row g-3">
                            <div className="col-6">
                              <label className="form-label text-muted">
                                Cart ID
                              </label>
                              <div className="fw-semibold">{cart.id}</div>
                            </div>
                            <div className="col-6">
                              <label className="form-label text-muted">
                                Created
                              </label>
                              <div className="fw-semibold">
                                {formatDate(cart.createdAt)}
                              </div>
                            </div>
                            <div className="col-6">
                              <label className="form-label text-muted">
                                Last Updated
                              </label>
                              <div className="fw-semibold">
                                {formatDate(cart.updatedAt)}
                              </div>
                            </div>
                            <div className="col-6">
                              <label className="form-label text-muted">
                                Last Activity
                              </label>
                              <div className="fw-semibold">
                                {formatDate(cart.lastActivity)}
                              </div>
                            </div>
                          </div>
                        </Card.Body>
                      </Card>
                    </Col>
                    <Col md={6}>
                      <Card className="border">
                        <Card.Body>
                          <h6 className="card-title">Customer Information</h6>
                          <div className="row g-3">
                            <div className="col-6">
                              <label className="form-label text-muted">
                                Customer Name
                              </label>
                              <div className="fw-semibold">
                                {getCustomerName(cart)}
                              </div>
                            </div>
                            <div className="col-6">
                              <label className="form-label text-muted">
                                Email
                              </label>
                              <div className="fw-semibold">
                                {cart.user.email}
                              </div>
                            </div>
                            <div className="col-6">
                              <label className="form-label text-muted">
                                Phone
                              </label>
                              <div className="fw-semibold">
                                {cart.user.phone || "N/A"}
                              </div>
                            </div>
                            <div className="col-6">
                              <label className="form-label text-muted">
                                Status
                              </label>
                              <div className="fw-semibold">
                                {cart.user.active ? (
                                  <Badge bg="success">Active</Badge>
                                ) : (
                                  <Badge bg="danger">Inactive</Badge>
                                )}
                              </div>
                            </div>
                          </div>
                        </Card.Body>
                      </Card>
                    </Col>
                  </Row>

                  {/* Cart Summary Stats */}
                  <Row className="mb-4">
                    <Col md={4}>
                      <Card className="border text-center">
                        <Card.Body>
                          <div className="text-muted mb-2">Total Items</div>
                          <div className="h3 text-primary mb-0">
                            {getCalculatedItemCount(cart)}
                          </div>
                        </Card.Body>
                      </Card>
                    </Col>
                    <Col md={4}>
                      <Card className="border text-center">
                        <Card.Body>
                          <div className="text-muted mb-2">Total Value</div>
                          <div className="h3 text-success mb-0">
                            $ {getCalculatedTotalValue(cart).toFixed(2)}
                          </div>
                        </Card.Body>
                      </Card>
                    </Col>
                    <Col md={4}>
                      <Card className="border text-center">
                        <Card.Body>
                          <div className="text-muted mb-2">Sections</div>
                          <div className="h3 text-info mb-0">
                            {cart.cartSections.length}
                          </div>
                        </Card.Body>
                      </Card>
                    </Col>
                  </Row>

                  {/* Cart Item Management */}
                  <CartItemManagement cart={cart} onCartUpdated={setCart} />

                  {/* Actions */}
                  <div className="d-flex gap-2 justify-content-between align-items-center mt-4">
                    <Link
                      to="/managements-carts"
                      className="btn btn-outline-secondary"
                    >
                      <i className="bx bx-arrow-back me-1"></i>
                      Back to Carts
                    </Link>
                    <Link
                      to="#"
                      className="btn btn-outline-danger"
                      onClick={(e) => {
                        e.preventDefault();
                        handleDeleteCart();
                      }}
                      style={{ pointerEvents: isDeleting ? "none" : "auto" }}
                    >
                      <i className="bx bx-trash me-1"></i>
                      {isDeleting ? "Deleting..." : "Delete Cart"}
                    </Link>
                  </div>
                </>
              ) : (
                <div
                  className="d-flex justify-content-center align-items-center"
                  style={{ minHeight: "400px" }}
                >
                  <Alert variant="warning">
                    <i className="bx bx-info-circle me-2"></i>
                    Cart not found or failed to load.
                  </Alert>
                </div>
              )}
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Fragment>
  );
};

export default ManagementCartDetails;
