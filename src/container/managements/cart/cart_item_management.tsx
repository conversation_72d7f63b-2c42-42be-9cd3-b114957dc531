import React, { FC, Fragment, useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  Card,
  Col,
  Form,
  Row,
  Table,
  Modal,
  Badge,
  Alert,
} from "react-bootstrap";
import LazyVariantSelect from "../../../components/lazy-select/lazy-variant-select";
import {
  useAddProductToCartMutation,
  useModifyCartItemMutation,
  useRemoveCartSectionMutation,
} from "../../../services/cart";
import {
  TCart,
  TCartSection,
  TCartItem,
  TAddProductToCartRequest,
  TModifyCartItemRequest,
} from "../../../types/cart";
import { hasPermission } from "../../../utils/authorization";
import { ACTION, RESOURCE } from "../../../utils/constant/authorization";
import Swal from "sweetalert2";
import moment from "moment";

interface CartItemManagementProps {
  cart: TCart;
  onCartUpdated: (updatedCart: TCart) => void;
}

const CartItemManagement: FC<CartItemManagementProps> = ({
  cart,
  onCartUpdated,
}) => {
  const [showAddProductModal, setShowAddProductModal] = useState(false);
  const [selectedVariant, setSelectedVariant] = useState<any>(null);

  const [addProductToCart, { isLoading: isAddingProduct }] =
    useAddProductToCartMutation();
  const [modifyCartItem, { isLoading: isModifyingItem }] =
    useModifyCartItemMutation();
  const [removeCartSection, { isLoading: isRemovingSection }] =
    useRemoveCartSectionMutation();

  const [removingItemId, setRemovingItemId] = useState<string | null>(null);

  useEffect(() => {
    if (cart) {
      const recalculatedCart = recalculateCartTotals(cart);
      if (
        recalculatedCart.itemCount !== cart.itemCount ||
        recalculatedCart.totalValue !== cart.totalValue ||
        recalculatedCart.cartSections.length !== cart.cartSections.length
      ) {
        onCartUpdated(recalculatedCart);
      }
    }
  }, [cart]);

  const recalculateCartTotals = (updatedCart: TCart): TCart => {
    if (!updatedCart || !updatedCart.cartSections) {
      return updatedCart;
    }

    const validSections = updatedCart.cartSections.filter(
      (section: TCartSection) =>
        section &&
        section.cartItems &&
        Array.isArray(section.cartItems) &&
        section.cartItems.length > 0
    );

    const totalItems = validSections.reduce(
      (sum: number, section: TCartSection) => {
        if (!section.cartItems || !Array.isArray(section.cartItems)) {
          return sum;
        }
        return (
          sum +
          section.cartItems.reduce((sectionSum: number, item: TCartItem) => {
            if (!item || typeof item.quantity !== "number") {
              return sectionSum;
            }
            return sectionSum + (item.quantity || 0);
          }, 0)
        );
      },
      0
    );

    const totalValue = validSections.reduce(
      (sum: number, section: TCartSection) => {
        if (!section.cartItems || !Array.isArray(section.cartItems)) {
          return sum;
        }
        return (
          sum +
          section.cartItems.reduce((sectionSum: number, item: TCartItem) => {
            if (!item) {
              return sectionSum;
            }
            const price = parseFloat(item.price) || 0;
            const quantity = item.quantity || 0;
            return sectionSum + price * quantity;
          }, 0)
        );
      },
      0
    );

    return {
      ...updatedCart,
      itemCount: totalItems,
      totalValue: totalValue,
      cartSections: validSections.map((section: TCartSection) => {
        if (!section.cartItems || !Array.isArray(section.cartItems)) {
          return {
            ...section,
            total: "0.00",
            quantity: 0,
          };
        }

        const sectionTotal = section.cartItems.reduce(
          (sum: number, item: TCartItem) => {
            if (!item) {
              return sum;
            }
            const price = parseFloat(item.price) || 0;
            const quantity = item.quantity || 0;
            return sum + price * quantity;
          },
          0
        );
        const sectionQuantity = section.cartItems.reduce(
          (sum: number, item: TCartItem) => {
            if (!item || typeof item.quantity !== "number") {
              return sum;
            }
            return sum + (item.quantity || 0);
          },
          0
        );

        return {
          ...section,
          total: sectionTotal.toFixed(2),
          quantity: sectionQuantity,
        };
      }),
    };
  };

  const handleAddProduct = async () => {
    if (!selectedVariant) {
      await Swal.fire({
        title: "Error!",
        text: "Please select a product variant.",
        icon: "error",
      });
      return;
    }

    if (!hasPermission(ACTION.CREATE, RESOURCE.CART)) {
      await Swal.fire({
        title: "Error!",
        text: "You don't have permission to add items to cart.",
        icon: "error",
      });
      return;
    }

    try {
      const requestData: TAddProductToCartRequest = {
        variantId: selectedVariant.id,
        quantity: 1,
      };

      const response = await addProductToCart({
        cartId: cart.id,
        data: requestData,
      }).unwrap();

      if (response && (response as any).success) {
        setShowAddProductModal(false);
        setSelectedVariant(null);

        const newSection = (response as any).data;

        const updatedCart = {
          ...cart,
          cartSections: Array.isArray(cart.cartSections)
            ? [...cart.cartSections]
            : [],
        };

        const existingSectionIndex = updatedCart.cartSections.findIndex(
          (section) => section.id === newSection.id
        );

        if (existingSectionIndex >= 0) {
          updatedCart.cartSections[existingSectionIndex] = { ...newSection };
        } else {
          updatedCart.cartSections.push({ ...newSection });
        }

        const recalculatedCart = recalculateCartTotals(updatedCart);
        onCartUpdated(recalculatedCart);
      } else {
        throw new Error("Add product failed");
      }
    } catch (error) {
      await Swal.fire({
        title: "Error!",
        text: "Failed to add product to cart. Please try again.",
        icon: "error",
      });
    }
  };

  const handleQuickQuantityChange = async (
    item: TCartItem,
    newQuantity: number
  ) => {
    if (!hasPermission(ACTION.UPDATE, RESOURCE.CART)) {
      return;
    }

    if (newQuantity < 0) return;

    try {
      setRemovingItemId(item.id);
      const requestData: TModifyCartItemRequest = {
        quantity: newQuantity,
      };

      const response = await modifyCartItem({
        cartItemId: item.id,
        data: requestData,
      }).unwrap();

      if (response && (response as any).success && (response as any).data) {
        const cartItemData = (response as any).data;

        if (cartItemData && cartItemData.cartSection) {
          const updatedCart = {
            ...cart,
            cartSections: Array.isArray(cart.cartSections)
              ? [...cart.cartSections]
              : [],
          };

          const sectionIndex = updatedCart.cartSections.findIndex(
            (section) => section.id === cartItemData.cartSection.id
          );

          if (sectionIndex >= 0) {
            const validItems = cartItemData.cartSection.cartItems.filter(
              (cartItem: TCartItem) => cartItem.quantity > 0
            );

            if (validItems.length === 0) {
              updatedCart.cartSections.splice(sectionIndex, 1);
            } else {
              updatedCart.cartSections[sectionIndex] = {
                ...cartItemData.cartSection,
                cartItems: validItems,
              };
            }

            const recalculatedCart = recalculateCartTotals(updatedCart);
            onCartUpdated(recalculatedCart);
          } else {
            throw new Error("Cart section not found");
          }
        } else {
          throw new Error("Invalid cart item data in response");
        }
      } else if (response && (response as any).success) {
        const updatedCart = {
          ...cart,
          cartSections: Array.isArray(cart.cartSections)
            ? [...cart.cartSections]
            : [],
        };
        const sectionIndex = updatedCart.cartSections.findIndex(
          (section) =>
            Array.isArray(section.cartItems) &&
            section.cartItems.some((ci) => ci.id === item.id)
        );
        if (sectionIndex >= 0) {
          if (typeof newQuantity === "number" && newQuantity === 0) {
            updatedCart.cartSections[sectionIndex] = {
              ...updatedCart.cartSections[sectionIndex],
              cartItems: updatedCart.cartSections[
                sectionIndex
              ].cartItems.filter((ci) => ci.id !== item.id),
            };
          } else {
            updatedCart.cartSections[sectionIndex] = {
              ...updatedCart.cartSections[sectionIndex],
              cartItems: updatedCart.cartSections[sectionIndex].cartItems.map(
                (ci) =>
                  ci.id === item.id ? { ...ci, quantity: newQuantity } : ci
              ),
            };
          }
          if (updatedCart.cartSections[sectionIndex].cartItems.length === 0) {
            updatedCart.cartSections.splice(sectionIndex, 1);
          }
        }
        const recalculatedCart = recalculateCartTotals(updatedCart);
        onCartUpdated(recalculatedCart);
      } else if (
        response &&
        (response as any).id &&
        (response as any).cartSections
      ) {
        onCartUpdated(response as any);
      } else {
        throw new Error("Invalid response from server");
      }
    } catch (error) {
      await Swal.fire({
        title: "Error!",
        text: "Failed to update quantity. Please try again.",
        icon: "error",
      });
    } finally {
      setRemovingItemId(null);
    }
  };

  const handleRemoveCartItem = async (item: TCartItem) => {
    if (!hasPermission(ACTION.DELETE, RESOURCE.CART)) {
      return;
    }

    try {
      setRemovingItemId(item.id);
      const requestData: TModifyCartItemRequest = {
        quantity: 0,
      };

      const response = await modifyCartItem({
        cartItemId: item.id,
        data: requestData,
      }).unwrap();

      if (response && (response as any).success && (response as any).data) {
        const cartItemData = (response as any).data;

        if (cartItemData && cartItemData.cartSection) {
          const updatedCart = {
            ...cart,
            cartSections: Array.isArray(cart.cartSections)
              ? [...cart.cartSections]
              : [],
          };

          const sectionIndex = updatedCart.cartSections.findIndex(
            (section) => section.id === cartItemData.cartSection.id
          );

          if (sectionIndex >= 0) {
            const validItems = cartItemData.cartSection.cartItems.filter(
              (cartItem: TCartItem) => cartItem.quantity > 0
            );

            if (validItems.length === 0) {
              updatedCart.cartSections.splice(sectionIndex, 1);
            } else {
              updatedCart.cartSections[sectionIndex] = {
                ...cartItemData.cartSection,
                cartItems: validItems,
              };
            }

            const recalculatedCart = recalculateCartTotals(updatedCart);
            onCartUpdated(recalculatedCart);
          } else {
            throw new Error("Cart section not found");
          }
        } else {
          throw new Error("Invalid cart item data in response");
        }
      } else if (response && (response as any).success) {
        const updatedCart = {
          ...cart,
          cartSections: Array.isArray(cart.cartSections)
            ? [...cart.cartSections]
            : [],
        };
        const sectionIndex = updatedCart.cartSections.findIndex(
          (section) =>
            Array.isArray(section.cartItems) &&
            section.cartItems.some((ci) => ci.id === item.id)
        );
        if (sectionIndex >= 0) {
          updatedCart.cartSections[sectionIndex] = {
            ...updatedCart.cartSections[sectionIndex],
            cartItems: updatedCart.cartSections[sectionIndex].cartItems.filter(
              (ci) => ci.id !== item.id
            ),
          };
          if (updatedCart.cartSections[sectionIndex].cartItems.length === 0) {
            updatedCart.cartSections.splice(sectionIndex, 1);
          }
        }
        const recalculatedCart = recalculateCartTotals(updatedCart);
        onCartUpdated(recalculatedCart);
      } else if (
        response &&
        (response as any).id &&
        (response as any).cartSections
      ) {
        onCartUpdated(response as any);
      } else {
        throw new Error("Invalid response from server");
      }
    } catch (error) {
      await Swal.fire({
        title: "Error!",
        text: "Failed to remove cart item. Please try again.",
        icon: "error",
      });
    } finally {
      setRemovingItemId(null);
    }
  };

  const handleRemoveSection = async (section: TCartSection) => {
    if (!hasPermission(ACTION.DELETE, RESOURCE.CART)) {
      return;
    }

    try {
      const response = await removeCartSection({
        cartSectionId: section.id,
        data: {},
      }).unwrap();

      if (response && response.message) {
        const updatedCart = {
          ...cart,
          cartSections: Array.isArray(cart.cartSections)
            ? [...cart.cartSections]
            : [],
        };
        updatedCart.cartSections = updatedCart.cartSections.filter(
          (cartSection) => cartSection.id !== section.id
        );

        const recalculatedCart = recalculateCartTotals(updatedCart);
        onCartUpdated(recalculatedCart);
      } else {
        throw new Error("Invalid response from server");
      }
    } catch (error) {
      await Swal.fire({
        title: "Error!",
        text: "Failed to remove cart section. Please try again.",
        icon: "error",
      });
    }
  };

  const openAddProductModal = () => {
    setShowAddProductModal(true);
  };

  const formatDate = (dateString: string) => {
    return moment(dateString).format("MMM DD, YYYY HH:mm:ss");
  };

  return (
    <Fragment>
      <Row className="mb-3">
        <Col>
          <div className="d-flex gap-2">
            <Button
              variant="primary"
              onClick={() => openAddProductModal()}
              disabled={
                !hasPermission(ACTION.CREATE, RESOURCE.CART) ||
                isAddingProduct ||
                isModifyingItem ||
                isRemovingSection ||
                !!removingItemId
              }
            >
              {isAddingProduct ? (
                <>
                  <div
                    className="spinner-border spinner-border-sm me-2"
                    role="status"
                  >
                    <span className="visually-hidden">Loading...</span>
                  </div>
                  Adding...
                </>
              ) : (
                <>
                  <i className="bx bx-plus me-1"></i>
                  Add Product
                </>
              )}
            </Button>
          </div>
        </Col>
      </Row>

      {cart.cartSections && cart.cartSections.length > 0 ? (
        cart.cartSections.map((section: TCartSection) => (
          <Card key={section.id} className="mb-3 border">
            <Card.Header className="d-flex justify-content-between align-items-center">
              <div>
                <h6 className="mb-0">
                  {section.section === "bundle" ? (
                    <i className="bx bx-package me-2"></i>
                  ) : (
                    <i className="bx bx-box me-2"></i>
                  )}
                  {section.title ||
                    `${
                      section.section.charAt(0).toUpperCase() +
                      section.section.slice(1)
                    } Items`}
                </h6>
                {section.bundleId && (
                  <small className="text-muted">
                    Bundle ID: {section.bundleId}
                  </small>
                )}
                {section.section === "product" && (
                  <small className="text-muted d-block">
                    <i className="bx bx-package me-1"></i>
                    {section.cartItems.length} product(s) in this section
                    {section.cartItems.length > 1 && (
                      <span className="text-success ms-2">
                        <i className="bx bx-check-circle"></i> Multi-product
                        section
                      </span>
                    )}
                  </small>
                )}
              </div>
              <div className="d-flex gap-2 align-items-center">
                <div className="btn-group">
                  <Button
                    variant="outline-danger"
                    size="sm"
                    onClick={() => handleRemoveSection(section)}
                    disabled={
                      !hasPermission(ACTION.DELETE, RESOURCE.CART) ||
                      isRemovingSection ||
                      !!removingItemId
                    }
                  >
                    {isRemovingSection ? (
                      <div
                        className="spinner-border spinner-border-sm"
                        role="status"
                      >
                        <span className="visually-hidden">Loading...</span>
                      </div>
                    ) : (
                      <i className="bx bx-trash"></i>
                    )}
                  </Button>
                </div>
              </div>
            </Card.Header>
            <Card.Body>
              <Table className="table table-bordered text-nowrap border-bottom">
                <thead className="table-dark">
                  <tr>
                    <th>
                      <i className="bx bx-image me-1"></i>Image
                    </th>
                    <th>
                      <i className="bx bx-box me-1"></i>Product
                    </th>
                    <th>
                      <i className="bx bx-tag me-1"></i>Variant
                    </th>
                    <th>
                      <i className="bx bx-barcode me-1"></i>SKU
                    </th>
                    <th className="text-center">
                      <i className="bx bx-hash me-1"></i>Quantity
                    </th>
                    <th className="text-center">
                      <i className="bx bx-dollar me-1"></i>Price
                    </th>
                    <th className="text-center">
                      <i className="bx bx-calculator me-1"></i>Total
                    </th>
                    <th className="text-center">
                      <i className="bx bx-cog me-1"></i>Actions
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {section.cartItems.map((item: TCartItem) => (
                    <tr key={item.id}>
                      <td className="text-center">
                        <span className="avatar avatar-lg bd-gray-200">
                          {item.image ? (
                            <img src={item.image} alt="" />
                          ) : (
                            <i className="bx bx-image fs-4"></i>
                          )}
                        </span>
                      </td>
                      <td>{item.productName}</td>
                      <td>{item.variantName}</td>
                      <td>{item.sku}</td>
                      <td className="text-center">
                        <div className="d-flex align-items-center justify-content-center gap-1">
                          <Button
                            variant="outline-secondary"
                            size="sm"
                            style={{ padding: "2px 6px", fontSize: "10px" }}
                            onClick={() =>
                              handleQuickQuantityChange(item, item.quantity - 1)
                            }
                            disabled={
                              item.quantity <= 1 ||
                              !hasPermission(ACTION.UPDATE, RESOURCE.CART) ||
                              isModifyingItem ||
                              isRemovingSection ||
                              removingItemId === item.id
                            }
                            title="Decrease quantity"
                          >
                            {removingItemId === item.id ? (
                              <div
                                className="spinner-border spinner-border-sm"
                                role="status"
                              >
                                <span className="visually-hidden">
                                  Loading...
                                </span>
                              </div>
                            ) : (
                              <i className="bx bx-minus"></i>
                            )}
                          </Button>
                          <Badge bg="primary" className="mx-1">
                            {item.quantity}
                          </Badge>
                          <Button
                            variant="outline-secondary"
                            size="sm"
                            style={{ padding: "2px 6px", fontSize: "10px" }}
                            onClick={() =>
                              handleQuickQuantityChange(item, item.quantity + 1)
                            }
                            disabled={
                              !hasPermission(ACTION.UPDATE, RESOURCE.CART) ||
                              isModifyingItem ||
                              isRemovingSection ||
                              removingItemId === item.id
                            }
                            title="Increase quantity"
                          >
                            {removingItemId === item.id ? (
                              <div
                                className="spinner-border spinner-border-sm"
                                role="status"
                              >
                                <span className="visually-hidden">
                                  Loading...
                                </span>
                              </div>
                            ) : (
                              <i className="bx bx-plus"></i>
                            )}
                          </Button>
                        </div>
                      </td>
                      <td className="text-center">
                        $ {parseFloat(item.price).toFixed(2)}
                      </td>
                      <td className="text-center">
                        $ {(parseFloat(item.price) * item.quantity).toFixed(2)}
                      </td>
                      <td className="text-center">
                        <Button
                          variant="outline-danger"
                          size="sm"
                          onClick={() => handleRemoveCartItem(item)}
                          disabled={
                            !hasPermission(ACTION.DELETE, RESOURCE.CART) ||
                            isModifyingItem ||
                            isRemovingSection ||
                            removingItemId === item.id
                          }
                          title="Remove this product from cart"
                        >
                          {removingItemId === item.id ? (
                            <div
                              className="spinner-border spinner-border-sm"
                              role="status"
                            >
                              <span className="visually-hidden">
                                Loading...
                              </span>
                            </div>
                          ) : (
                            <i className="bx bx-trash"></i>
                          )}
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </Table>

              {section.lastModifiedByAdminId && (
                <Alert variant="info" className="mt-3">
                  <small>
                    <strong>Last Admin Action:</strong>{" "}
                    {section.lastAdminAction} |<strong>Modified:</strong>{" "}
                    {section.lastAdminModifiedAt
                      ? formatDate(section.lastAdminModifiedAt)
                      : "N/A"}
                    {section.lastAdminModificationNotes && (
                      <>
                        {" "}
                        | <strong>Notes:</strong>{" "}
                        {section.lastAdminModificationNotes}
                      </>
                    )}
                  </small>
                </Alert>
              )}
            </Card.Body>
          </Card>
        ))
      ) : (
        <Card className="border">
          <Card.Body className="text-center text-muted">
            <i className="bx bx-cart bx-lg mb-2"></i>
            <div>This cart is empty</div>
          </Card.Body>
        </Card>
      )}

      <Modal
        show={showAddProductModal}
        onHide={() => setShowAddProductModal(false)}
        size="lg"
      >
        <Modal.Header closeButton>
          <Modal.Title>Add Product to Cart</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <Row>
              <Col md={12}>
                <Form.Group className="mb-3">
                  <Form.Label>Select Product Variant</Form.Label>
                  <LazyVariantSelect
                    selectionFunctionQuery={{ availableForSale: true }}
                    getSelectedOptions={(value) => setSelectedVariant(value)}
                    formatOptionLabel={(value) => (
                      <div className="d-flex align-items-center">
                        <span className="avatar avatar-sm bd-gray-200 me-2">
                          <img src={value.value.image?.src} alt="" />
                        </span>
                        <div>
                          <div className="fw-semibold">
                            {value.value.product?.title}
                          </div>
                          <div className="text-muted small">
                            {value.value.title}
                          </div>
                          <div className="text-muted small">
                            SKU: {value.value.sku}
                          </div>
                        </div>
                        <div className="ms-auto">
                          <Badge bg="success">${value.value.price}</Badge>
                        </div>
                      </div>
                    )}
                  />
                </Form.Group>
              </Col>
            </Row>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button
            variant="secondary"
            onClick={() => setShowAddProductModal(false)}
          >
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={handleAddProduct}
            disabled={!selectedVariant || isAddingProduct || !!removingItemId}
          >
            {isAddingProduct ? (
              <>
                <div
                  className="spinner-border spinner-border-sm me-2"
                  role="status"
                >
                  <span className="visually-hidden">Loading...</span>
                </div>
                Adding...
              </>
            ) : (
              "Add Product"
            )}
          </Button>
        </Modal.Footer>
      </Modal>
    </Fragment>
  );
};

export default CartItemManagement;
