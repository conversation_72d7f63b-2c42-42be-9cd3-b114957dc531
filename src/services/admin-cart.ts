/**
 * Admin Cart Management Service
 * Comprehensive cart management for admin users
 * 
 * Based on test results from 2025-08-15:
 * ✅ Basic cart operations (list, get by ID, get by user) - WORKING
 * ❌ Enhanced features (analytics, summary, suggestions, validation) - NOT IMPLEMENTED
 */

import { apiService } from "./api";
import type { TQueryAPI, TReponsePaging } from "./api";

// ============================================================================
// CART TYPES (Based on actual API responses)
// ============================================================================

export interface TAdminCart {
  id: string;
  userId: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  user?: TAdminCartUser;
  sections?: TAdminCartSection[];
}

export interface TAdminCartUser {
  id: string;
  email: string;
  firstName: string | null;
  lastName: string | null;
  phone: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface TAdminCartSection {
  id: string;
  cartId: string;
  type: "product" | "bundle";
  title: string;
  quantity: number;
  total: number;
  bundleId: string | null;
  cartItems: TAdminCartItem[];
}

export interface TAdminCartItem {
  id: string;
  cartSectionId: string;
  productId: string;
  variantId: string;
  quantity: number;
  price: number;
  product: TAdminCartProduct;
  variant: TAdminCartProductVariant;
}

export interface TAdminCartProduct {
  id: string;
  title: string;
  handle: string;
  status: "active" | "draft";
  price: string;
  image?: TAdminCartProductImage;
}

export interface TAdminCartProductVariant {
  id: string;
  title: string;
  price: string;
  sku: string | null;
  availableForSale: number;
  inventoryQuantity: number;
}

export interface TAdminCartProductImage {
  id: string;
  src: string;
  altText: string | null;
  position: number;
}

// ============================================================================
// REQUEST TYPES
// ============================================================================

export interface TAdminCartQueryParams extends TQueryAPI {
  userId?: string;
  status?: "active" | "abandoned" | "completed";
  dateFrom?: string;
  dateTo?: string;
}

export interface TAdminCartQuickAddRequest {
  products: TAdminCartQuickAddProduct[];
}

export interface TAdminCartQuickAddProduct {
  variantId: string;
  quantity: number;
  affiliateId?: string;
  notes?: string;
}

export interface TAdminCartBulkAddRequest {
  categoryId?: string;
  collectionId?: string;
  searchQuery?: string;
  limit?: number;
  affiliateId?: string;
  notes?: string;
}

export interface TAdminCartMoveItemRequest {
  cartId: string;
  cartItemId: string;
  targetSectionId: string;
}

export interface TAdminCartUpdateQuantityRequest {
  quantity: number;
}

// ============================================================================
// RESPONSE TYPES (For future enhanced features)
// ============================================================================

export interface TAdminCartAnalytics {
  totalItems: number;
  totalValue: number;
  sectionCount: number;
  categoryDistribution: Record<string, number>;
  priceRange: {
    min: number;
    max: number;
    average: number;
  };
  sections: TAdminCartSectionSummary[];
}

export interface TAdminCartSectionSummary {
  id: string;
  type: string;
  title: string;
  itemCount: number;
  totalValue: number;
}

export interface TAdminCartSummary {
  cart: TAdminCart;
  user: TAdminCartUser;
  summary: {
    totalItems: number;
    totalValue: number;
    sectionCount: number;
  };
  sections: TAdminCartSectionSummary[];
}

export interface TAdminCartValidation {
  isValid: boolean;
  issues: string[];
  warnings: string[];
  details: {
    invalidItems: string[];
    outOfStockItems: string[];
    priceChanges: string[];
  };
}

// ============================================================================
// ADMIN CART SERVICE
// ============================================================================

export const adminCartService = apiService.injectEndpoints({
  endpoints: (build) => ({
    // ========================================================================
    // BASIC CART OPERATIONS (✅ WORKING)
    // ========================================================================

    /**
     * Get all carts with pagination and filtering
     * GET /v1/admin/carts
     */
    getAdminCarts: build.query<TReponsePaging<TAdminCart>, TAdminCartQueryParams>({
      query: (params) => ({
        url: "v1/admin/carts",
        method: "GET",
        params,
      }),
    }),

    /**
     * Get cart by ID with full details
     * GET /v1/admin/carts/:id
     */
    getAdminCartById: build.query<TAdminCart, string>({
      query: (cartId) => ({
        url: `v1/admin/carts/${cartId}`,
        method: "GET",
      }),
    }),

    /**
     * Get carts by user ID
     * GET /v1/admin/carts/user/:userId
     */
    getAdminCartsByUserId: build.query<
      TReponsePaging<TAdminCart>,
      { userId: string; params?: TAdminCartQueryParams }
    >({
      query: ({ userId, params }) => ({
        url: `v1/admin/carts/user/${userId}`,
        method: "GET",
        params,
      }),
    }),

    // ========================================================================
    // CART MODIFICATION (Based on documentation - may not be implemented)
    // ========================================================================

    /**
     * Quick add multiple products to cart
     * POST /v1/admin/carts/:cartId/quick-add
     */
    quickAddProductsToCart: build.mutation<
      any,
      { cartId: string; request: TAdminCartQuickAddRequest }
    >({
      query: ({ cartId, request }) => ({
        url: `v1/admin/carts/${cartId}/quick-add`,
        method: "POST",
        body: request,
      }),
    }),

    /**
     * Bulk add products from category/collection/search
     * POST /v1/admin/carts/:cartId/bulk-add
     */
    bulkAddProductsToCart: build.mutation<
      any,
      { cartId: string; request: TAdminCartBulkAddRequest }
    >({
      query: ({ cartId, request }) => ({
        url: `v1/admin/carts/${cartId}/bulk-add`,
        method: "POST",
        body: request,
      }),
    }),

    /**
     * Move item between cart sections
     * PUT /v1/admin/carts/move-item
     */
    moveCartItem: build.mutation<any, TAdminCartMoveItemRequest>({
      query: (request) => ({
        url: "v1/admin/carts/move-item",
        method: "PUT",
        body: request,
      }),
    }),

    /**
     * Update cart section quantity
     * PUT /v1/admin/carts/sections/:sectionId/quantity
     */
    updateCartSectionQuantity: build.mutation<
      any,
      { sectionId: string; request: TAdminCartUpdateQuantityRequest }
    >({
      query: ({ sectionId, request }) => ({
        url: `v1/admin/carts/sections/${sectionId}/quantity`,
        method: "PUT",
        body: request,
      }),
    }),

    /**
     * Delete cart section
     * DELETE /v1/admin/carts/sections/:sectionId
     */
    deleteCartSection: build.mutation<any, string>({
      query: (sectionId) => ({
        url: `v1/admin/carts/sections/${sectionId}`,
        method: "DELETE",
      }),
    }),

    /**
     * Delete entire cart
     * DELETE /v1/admin/carts/:cartId
     */
    deleteCart: build.mutation<any, string>({
      query: (cartId) => ({
        url: `v1/admin/carts/${cartId}`,
        method: "DELETE",
      }),
    }),

    // ========================================================================
    // ENHANCED FEATURES (❌ NOT IMPLEMENTED - will return 404)
    // ========================================================================

    /**
     * Get cart analytics (NOT IMPLEMENTED)
     * GET /v1/admin/carts/:cartId/analytics
     */
    getCartAnalytics: build.query<TAdminCartAnalytics, string>({
      query: (cartId) => ({
        url: `v1/admin/carts/${cartId}/analytics`,
        method: "GET",
      }),
    }),

    /**
     * Get cart summary (NOT IMPLEMENTED)
     * GET /v1/admin/carts/:cartId/summary
     */
    getCartSummary: build.query<TAdminCartSummary, string>({
      query: (cartId) => ({
        url: `v1/admin/carts/${cartId}/summary`,
        method: "GET",
      }),
    }),

    /**
     * Get cart suggestions (NOT IMPLEMENTED)
     * GET /v1/admin/carts/suggestions
     */
    getCartSuggestions: build.query<any[], { cartId?: string; limit?: number }>({
      query: (params) => ({
        url: "v1/admin/carts/suggestions",
        method: "GET",
        params,
      }),
    }),

    /**
     * Validate cart integrity (NOT IMPLEMENTED)
     * GET /v1/admin/carts/:cartId/validate
     */
    validateCart: build.query<TAdminCartValidation, string>({
      query: (cartId) => ({
        url: `v1/admin/carts/${cartId}/validate`,
        method: "GET",
      }),
    }),
  }),
  overrideExisting: true,
});

// ============================================================================
// EXPORT HOOKS
// ============================================================================

export const {
  // Basic cart operations (✅ WORKING)
  useLazyGetAdminCartsQuery,
  useLazyGetAdminCartByIdQuery,
  useLazyGetAdminCartsByUserIdQuery,

  // Cart modification (⚠️ MAY NOT BE IMPLEMENTED)
  useQuickAddProductsToCartMutation,
  useBulkAddProductsToCartMutation,
  useMoveCartItemMutation,
  useUpdateCartSectionQuantityMutation,
  useDeleteCartSectionMutation,
  useDeleteCartMutation,

  // Enhanced features (❌ NOT IMPLEMENTED)
  useLazyGetCartAnalyticsQuery,
  useLazyGetCartSummaryQuery,
  useLazyGetCartSuggestionsQuery,
  useLazyValidateCartQuery,
} = adminCartService;
