import { apiService } from "./api";
import {
  TCart,
  TCartListParams,
  TAddProductToCartRequest,
  TModifyCartItemRequest,
  TRemoveCartSectionRequest,
} from "../types/cart";

const ENDPOINT = "v1/admin/carts";

export const cartService = apiService.injectEndpoints({
  endpoints: (build) => ({
    listCarts: build.query<TReponsePaging<TCart>, TCartListParams>({
      query: (params) => {
        return {
          url: ENDPOINT,
          method: "GET",
          params,
        };
      },
      transformResponse: (rawResult: TReponsePaging<TCart>) => {
        const data = rawResult.data?.map((item: TCart, index) => ({
          ...item,
          _rowIndex: index + 1,
        }));
        return { ...rawResult, data: data || [] };
      },
    }),
    getUserCart: build.query<TCart, string>({
      query: (userId: string) => {
        return {
          url: `${ENDPOINT}/user/${userId}`,
          method: "GET",
        };
      },
      transformResponse: (rawResult: TCart) => {
        return rawResult;
      },
    }),
    getCartDetails: build.query<TCart, string>({
      query: (cartId: string) => {
        return {
          url: `${ENDPOINT}/${cartId}`,
          method: "GET",
        };
      },
      transformResponse: (rawResult: TCart) => {
        return rawResult;
      },
    }),
    deleteCart: build.mutation<{ success: boolean }, string>({
      query: (cartId: string) => {
        return {
          url: `${ENDPOINT}/${cartId}`,
          method: "DELETE",
        };
      },
    }),
    addProductToCart: build.mutation<
      TCart,
      { cartId: string; data: TAddProductToCartRequest }
    >({
      query: ({ cartId, data }) => {
        return {
          url: `${ENDPOINT}/${cartId}/items`,
          method: "POST",
          data,
        };
      },
    }),
    modifyCartItem: build.mutation<
      TCart,
      { cartItemId: string; data: TModifyCartItemRequest }
    >({
      query: ({ cartItemId, data }) => {
        return {
          url: `${ENDPOINT}/items/${cartItemId}`,
          method: "PUT",
          data,
        };
      },
    }),
    removeCartSection: build.mutation<
      { message: string },
      { cartSectionId: string; data: TRemoveCartSectionRequest }
    >({
      query: ({ cartSectionId, data }) => {
        return {
          url: `${ENDPOINT}/sections/${cartSectionId}`,
          method: "DELETE",
          data,
        };
      },
    }),
  }),
  overrideExisting: true,
});

export const {
  useLazyListCartsQuery,
  useLazyGetUserCartQuery,
  useLazyGetCartDetailsQuery,
  useDeleteCartMutation,
  useAddProductToCartMutation,
  useModifyCartItemMutation,
  useRemoveCartSectionMutation,
} = cartService;
