/**
 * Admin Shop Service Test File
 * This file tests the basic functionality and type definitions
 */

import {
  adminShopService,
  buildSearchQuery,
  buildCategoryProductsQuery,
  buildCollectionProductsQuery,
} from "./admin-shop";
import type {
  TAdminShopProduct,
  TAdminShopCategory,
  TAdminShopCollection,
  TAdminShopSearchParams,
  TAdminShopCategoryProductsParams,
  TAdminShopCollectionProductsParams,
} from "./admin-shop";

// Test data for type checking (Updated based on actual API responses)
const testProduct: TAdminShopProduct = {
  id: "prod-123",
  shopifyProductId: "gid://shopify/Product/7890722062390",
  title: "Test Product",
  description: "Test product description",
  createdAt: "2025-07-21T09:43:40.000+00:00",
  updatedAt: "2025-08-07T15:57:51.000+00:00",
  publishedAt: "1969-12-31T19:00:00.000+00:00",
  handle: "test-product",
  status: "active",
  deletedAt: null,
  price: "29.99",
  vendorId: "vendor-123",
  productTypeId: "type-123",
  categoryId: "cat-123",
  pickupOnly: 0,
  fulfilProductId: null,
  classification: null,
  isGift: 0,
  pendingChanges: null,
  pendingApproval: 0,
  variants: [
    {
      id: "var-123",
      productId: "prod-123",
      title: "Test Variant",
      price: "29.99",
      compareAtPrice: "39.99",
      inventoryQuantity: 100,
      inventoryPolicy: "deny",
      sku: "TEST-VAR-123",
      availableForSale: 1,
      numberSold: 0,
      warehouseInventories: [],
    },
  ],
  productType: {
    id: "type-123",
    name: "Test Type",
    createdAt: "2025-01-15T19:45:44.000+00:00",
    updatedAt: "2025-07-21T09:57:38.000+00:00",
    deletedAt: null,
  },
  image: {
    id: "img-123",
    productId: "prod-123",
    shopifyImageId: null,
    src: "https://example.com/image.jpg",
    position: 1,
    createdAt: "2025-07-21T09:57:41.000+00:00",
    updatedAt: "2025-07-21T09:57:41.000+00:00",
    altText: "Test Product Image",
    width: 800,
    height: 600,
    deletedAt: null,
    variantId: null,
  },
  vendor: {
    id: "vendor-123",
    companyName: "Test Vendor",
    createdAt: "2025-01-07T19:42:01.000+00:00",
    updatedAt: "2025-07-24T15:10:45.000+00:00",
    deletedAt: null,
    brandName: null,
    website: null,
    contactName: "Test Contact",
    phone: null,
    email: "<EMAIL>",
    address1: null,
    address2: null,
    city: null,
    state: null,
    country: null,
    zipCode: null,
    ein: null,
    registrationStatus: "approved",
    rejectionReason: null,
    commissionRate: 0.1,
    fixedCommissionAmount: 5,
    warehouseId: "warehouse-123",
    thumbnailId: null,
    description: null,
    returnWarrantyPolicy: null,
    name: "Test Vendor",
  },
  tags: [
    {
      id: "tag-123",
      name: "test",
      createdAt: "2025-01-15T19:46:04.000+00:00",
      updatedAt: "2025-07-21T09:57:38.000+00:00",
      deletedAt: null,
    },
  ],
  reviewSummary: {
    latestReviews: [],
    totalReviews: 0,
    averageRating: 0,
    details: {
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
    },
  },
  onlineStoreUrl: "https://example.com/product/prod-123",
};

const testCategory: TAdminShopCategory = {
  id: "cat-123",
  shopifyId: "gid://shopify/TaxonomyCategory/ae-2-1-2-3-1",
  name: "Test Category",
  level: 6,
  isRoot: 0,
  isLeaf: 1,
  parentId: null,
  ancestorIds: [
    "gid://shopify/TaxonomyCategory/ae-2-1-2-3",
    "gid://shopify/TaxonomyCategory/ae-2-1-2",
    "gid://shopify/TaxonomyCategory/ae-2-1",
    "gid://shopify/TaxonomyCategory/ae-2",
    "gid://shopify/TaxonomyCategory/ae",
  ],
  childrenIds: [],
  fullName:
    "Arts & Entertainment > Hobbies & Creative Arts > Arts & Crafts > Art & Crafting Materials > Craft Paint, Ink & Glaze > Art & Craft Paint",
  isArchived: 0,
  createdAt: "2025-01-01T00:00:00Z",
  updatedAt: "2025-01-01T00:00:00Z",
  deletedAt: null,
  imageId: "img-123",
  image: {
    id: "img-123",
    fileKey: "2025/02/27/test-image.webp",
    url: "https://example.com/test-image.webp",
    createdAt: "2025-02-27T10:45:38.000+00:00",
    updatedAt: "2025-02-27T10:45:38.000+00:00",
    deletedAt: null,
    type: "image",
    resourceId: null,
    sourceFrom: null,
    start: null,
    end: null,
  },
};

const testCollection: TAdminShopCollection = {
  id: "col-123",
  shopifyCollectionId: "gid://shopify/Collection/283112505398",
  title: "Test Collection",
  description: "Test collection description",
  handle: "test-collection",
  imageUrl: "https://example.com/collection.jpg",
  imageAltText: null,
  status: 1,
  createdAt: "2025-01-01T00:00:00Z",
  updatedAt: "2025-01-01T00:00:00Z",
  deletedAt: null,
  imageId: null,
  image: null,
  categories: [testCategory],
  productsCount: 25,
};

// Test utility functions
const testSearchQuery: TAdminShopSearchParams = {
  q: "test product",
  page: 1,
  perPage: 20,
  categoryId: "cat-123",
  sortBy: "relevance",
  sortOrder: "desc",
};

const testCategoryProductsParams: TAdminShopCategoryProductsParams = {
  page: 1,
  perPage: 20,
  sort: "title",
  order: "asc",
  search: "test",
  minPrice: 10,
  maxPrice: 100,
  inStock: true,
};

const testCollectionProductsParams: TAdminShopCollectionProductsParams = {
  page: 1,
  perPage: 20,
  categoryId: "cat-123",
  orderBy: "BEST_SELLING",
  reverse: false,
  search: "test",
  minPrice: 10,
  maxPrice: 100,
  inStock: true,
};

// Test utility function calls
const builtSearchQuery = buildSearchQuery({ q: "test", page: 1 });
const builtCategoryQuery = buildCategoryProductsQuery("cat-123", {
  perPage: 50,
});
const builtCollectionQuery = buildCollectionProductsQuery("col-123", {
  perPage: 50,
});

// Export for testing purposes
export {
  testProduct,
  testCategory,
  testCollection,
  testSearchQuery,
  testCategoryProductsParams,
  testCollectionProductsParams,
  builtSearchQuery,
  builtCategoryQuery,
  builtCollectionQuery,
};

// Type checking function
export function validateTypes(): boolean {
  try {
    // Test product type
    if (typeof testProduct.id !== "string") return false;
    if (typeof testProduct.title !== "string") return false;
    if (testProduct.status !== "active" && testProduct.status !== "draft")
      return false;
    if (typeof testProduct.price !== "string") return false;
    if (!Array.isArray(testProduct.variants)) return false;
    if (typeof testProduct.vendor.companyName !== "string") return false;

    // Test category type
    if (typeof testCategory.id !== "string") return false;
    if (typeof testCategory.name !== "string") return false;
    if (typeof testCategory.level !== "number") return false;
    if (!Array.isArray(testCategory.ancestorIds)) return false;

    // Test collection type
    if (typeof testCollection.id !== "string") return false;
    if (typeof testCollection.title !== "string") return false;
    if (typeof testCollection.status !== "number") return false;
    if (!Array.isArray(testCollection.categories)) return false;

    // Test utility functions
    if (typeof builtSearchQuery.q !== "string") return false;
    if (typeof builtCategoryQuery.categoryId !== "string") return false;
    if (typeof builtCollectionQuery.collectionId !== "string") return false;

    return true;
  } catch (error) {
    console.error("Type validation failed:", error);
    return false;
  }
}

// Service validation function
export function validateService(): boolean {
  try {
    // Check if service has required endpoints
    const endpoints = Object.keys(adminShopService.endpoints);
    const requiredEndpoints = [
      "getCategories",
      "getCategoryProducts",
      "getCollections",
      "getCollectionProducts",
      "searchProducts",
      "getProductDetails",
      "getProductSuggestions",
      "quickAddProducts",
      "getRecentProducts",
      "getPopularProducts",
    ];

    const hasAllEndpoints = requiredEndpoints.every((endpoint) =>
      endpoints.includes(endpoint)
    );

    return hasAllEndpoints;
  } catch (error) {
    console.error("Service validation failed:", error);
    return false;
  }
}

// Run validation
if (typeof window === "undefined") {
  // Node.js environment
  console.log("Admin Shop Service Validation:");
  console.log("Types valid:", validateTypes());
  console.log("Service valid:", validateService());
}
