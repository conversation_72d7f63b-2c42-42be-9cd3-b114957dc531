/**
 * Comprehensive Admin Shop Service Test File
 * Tests all endpoints with provided JWT token and validates response structures
 */

import axios from "axios";
import {
  adminShopService,
  buildSearchQuery,
  buildCategoryProductsQuery,
  buildCollectionProductsQuery,
} from "./admin-shop";
import type {
  TAdminShopProduct,
  TAdminShopCategory,
  TAdminShopCollection,
  TAdminShopSearchParams,
  TAdminShopCategoryProductsParams,
  TAdminShopCollectionProductsParams,
} from "./admin-shop";

// JWT Token for testing
const TEST_JWT_TOKEN =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiJjNzJhOGJkYi01YTJmLTQzZjItYmEyNy1mNzcwYTBkNDJjYjMiLCJpYXQiOjE3NTUyMjg0Nzd9.uewa4XL6cvE3sRrD7ozolbIFpQ06bb0dc1_-rfQezTs";

// API Base URL
const API_BASE_URL = process.env.VITE_APP_API_URL || "https://api.zurno.com";
const ENDPOINT = "v1/admin/shop";

// Test Results Interface
interface TestResults {
  endpoint: string;
  method: string;
  status: "success" | "error" | "not_tested";
  statusCode?: number;
  responseTime?: number;
  data?: any;
  error?: string;
  responseStructure?: any;
}

// Configure axios with JWT token
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    Authorization: `Bearer ${TEST_JWT_TOKEN}`,
    "Content-Type": "application/json",
  },
  timeout: 30000, // 30 seconds timeout
});

// Test utility functions
const logTest = (
  testName: string,
  status: "start" | "success" | "error",
  details?: any
) => {
  const timestamp = new Date().toISOString();
  const emoji = status === "start" ? "🧪" : status === "success" ? "✅" : "❌";
  console.log(`${emoji} [${timestamp}] ${testName}`, details ? details : "");
};

const analyzeResponseStructure = (data: any): any => {
  if (Array.isArray(data)) {
    return {
      type: "array",
      length: data.length,
      firstItemStructure:
        data.length > 0 ? analyzeResponseStructure(data[0]) : null,
    };
  } else if (typeof data === "object" && data !== null) {
    const structure: any = { type: "object", properties: {} };
    for (const key in data) {
      if (data.hasOwnProperty(key)) {
        structure.properties[key] = {
          type: typeof data[key],
          isNull: data[key] === null,
          isArray: Array.isArray(data[key]),
        };
      }
    }
    return structure;
  } else {
    return { type: typeof data, value: data };
  }
};

// Test functions for each endpoint
export const testGetCategories = async (): Promise<TestResults> => {
  const testName = "GET Categories";
  logTest(testName, "start");

  const startTime = Date.now();
  try {
    const response = await apiClient.get(`${ENDPOINT}/categories`);
    const responseTime = Date.now() - startTime;

    logTest(testName, "success", {
      statusCode: response.status,
      dataLength: response.data?.data?.length,
      responseTime: `${responseTime}ms`,
    });

    return {
      endpoint: `${ENDPOINT}/categories`,
      method: "GET",
      status: "success",
      statusCode: response.status,
      responseTime,
      data: response.data,
      responseStructure: analyzeResponseStructure(response.data),
    };
  } catch (error: any) {
    const responseTime = Date.now() - startTime;
    logTest(testName, "error", {
      error: error.message,
      statusCode: error.response?.status,
      responseTime: `${responseTime}ms`,
    });

    return {
      endpoint: `${ENDPOINT}/categories`,
      method: "GET",
      status: "error",
      statusCode: error.response?.status,
      responseTime,
      error: error.message,
      data: error.response?.data,
    };
  }
};

export const testGetCollections = async (): Promise<TestResults> => {
  const testName = "GET Collections";
  logTest(testName, "start");

  const startTime = Date.now();
  try {
    const response = await apiClient.get(`${ENDPOINT}/collections`);
    const responseTime = Date.now() - startTime;

    logTest(testName, "success", {
      statusCode: response.status,
      dataLength: response.data?.data?.length,
      responseTime: `${responseTime}ms`,
    });

    return {
      endpoint: `${ENDPOINT}/collections`,
      method: "GET",
      status: "success",
      statusCode: response.status,
      responseTime,
      data: response.data,
      responseStructure: analyzeResponseStructure(response.data),
    };
  } catch (error: any) {
    const responseTime = Date.now() - startTime;
    logTest(testName, "error", {
      error: error.message,
      statusCode: error.response?.status,
      responseTime: `${responseTime}ms`,
    });

    return {
      endpoint: `${ENDPOINT}/collections`,
      method: "GET",
      status: "error",
      statusCode: error.response?.status,
      responseTime,
      error: error.message,
      data: error.response?.data,
    };
  }
};

export const testGetCategoryProducts = async (
  categoryId: string
): Promise<TestResults> => {
  const testName = `GET Category Products (${categoryId})`;
  logTest(testName, "start");

  const startTime = Date.now();
  try {
    const response = await apiClient.get(
      `${ENDPOINT}/categories/${categoryId}/products`,
      {
        params: {
          page: 1,
          perPage: 10,
          sort: "title",
          order: "asc",
        },
      }
    );
    const responseTime = Date.now() - startTime;

    logTest(testName, "success", {
      statusCode: response.status,
      dataLength: response.data?.data?.length,
      responseTime: `${responseTime}ms`,
    });

    return {
      endpoint: `${ENDPOINT}/categories/${categoryId}/products`,
      method: "GET",
      status: "success",
      statusCode: response.status,
      responseTime,
      data: response.data,
      responseStructure: analyzeResponseStructure(response.data),
    };
  } catch (error: any) {
    const responseTime = Date.now() - startTime;
    logTest(testName, "error", {
      error: error.message,
      statusCode: error.response?.status,
      responseTime: `${responseTime}ms`,
    });

    return {
      endpoint: `${ENDPOINT}/categories/${categoryId}/products`,
      method: "GET",
      status: "error",
      statusCode: error.response?.status,
      responseTime,
      error: error.message,
      data: error.response?.data,
    };
  }
};

export const testGetCollectionProducts = async (
  collectionId: string
): Promise<TestResults> => {
  const testName = `GET Collection Products (${collectionId})`;
  logTest(testName, "start");

  const startTime = Date.now();
  try {
    const response = await apiClient.get(
      `${ENDPOINT}/collections/${collectionId}/products`,
      {
        params: {
          page: 1,
          perPage: 10,
          orderBy: "BEST_SELLING",
          reverse: false,
        },
      }
    );
    const responseTime = Date.now() - startTime;

    logTest(testName, "success", {
      statusCode: response.status,
      productsLength: response.data?.products?.length,
      responseTime: `${responseTime}ms`,
    });

    return {
      endpoint: `${ENDPOINT}/collections/${collectionId}/products`,
      method: "GET",
      status: "success",
      statusCode: response.status,
      responseTime,
      data: response.data,
      responseStructure: analyzeResponseStructure(response.data),
    };
  } catch (error: any) {
    const responseTime = Date.now() - startTime;
    logTest(testName, "error", {
      error: error.message,
      statusCode: error.response?.status,
      responseTime: `${responseTime}ms`,
    });

    return {
      endpoint: `${ENDPOINT}/collections/${collectionId}/products`,
      method: "GET",
      status: "error",
      statusCode: error.response?.status,
      responseTime,
      error: error.message,
      data: error.response?.data,
    };
  }
};

export const testSearchProducts = async (
  query: string
): Promise<TestResults> => {
  const testName = `Search Products (${query})`;
  logTest(testName, "start");

  const startTime = Date.now();
  try {
    const response = await apiClient.get(`${ENDPOINT}/search`, {
      params: {
        q: query,
        page: 1,
        perPage: 10,
        sortBy: "relevance",
        sortOrder: "desc",
      },
    });
    const responseTime = Date.now() - startTime;

    logTest(testName, "success", {
      statusCode: response.status,
      responseTime: `${responseTime}ms`,
    });

    return {
      endpoint: `${ENDPOINT}/search`,
      method: "GET",
      status: "success",
      statusCode: response.status,
      responseTime,
      data: response.data,
      responseStructure: analyzeResponseStructure(response.data),
    };
  } catch (error: any) {
    const responseTime = Date.now() - startTime;
    logTest(testName, "error", {
      error: error.message,
      statusCode: error.response?.status,
      responseTime: `${responseTime}ms`,
    });

    return {
      endpoint: `${ENDPOINT}/search`,
      method: "GET",
      status: "error",
      statusCode: error.response?.status,
      responseTime,
      error: error.message,
      data: error.response?.data,
    };
  }
};

export const testGetProductDetails = async (
  productId: string
): Promise<TestResults> => {
  const testName = `GET Product Details (${productId})`;
  logTest(testName, "start");

  const startTime = Date.now();
  try {
    const response = await apiClient.get(`${ENDPOINT}/products/${productId}`, {
      params: {
        includeVariants: true,
        includeImages: true,
        includeCollections: true,
        includeCategory: true,
        includeReviews: true,
      },
    });
    const responseTime = Date.now() - startTime;

    logTest(testName, "success", {
      statusCode: response.status,
      productTitle: response.data?.title,
      variantsCount: response.data?.variants?.length,
      responseTime: `${responseTime}ms`,
    });

    return {
      endpoint: `${ENDPOINT}/products/${productId}`,
      method: "GET",
      status: "success",
      statusCode: response.status,
      responseTime,
      data: response.data,
      responseStructure: analyzeResponseStructure(response.data),
    };
  } catch (error: any) {
    const responseTime = Date.now() - startTime;
    logTest(testName, "error", {
      error: error.message,
      statusCode: error.response?.status,
      responseTime: `${responseTime}ms`,
    });

    return {
      endpoint: `${ENDPOINT}/products/${productId}`,
      method: "GET",
      status: "error",
      statusCode: error.response?.status,
      responseTime,
      error: error.message,
      data: error.response?.data,
    };
  }
};

export const testGetProductSuggestions = async (): Promise<TestResults> => {
  const testName = "GET Product Suggestions";
  logTest(testName, "start");

  const startTime = Date.now();
  try {
    const response = await apiClient.get(`${ENDPOINT}/suggestions`, {
      params: {
        perPage: 10,
        includeOutOfStock: false,
      },
    });
    const responseTime = Date.now() - startTime;

    logTest(testName, "success", {
      statusCode: response.status,
      responseTime: `${responseTime}ms`,
    });

    return {
      endpoint: `${ENDPOINT}/suggestions`,
      method: "GET",
      status: "success",
      statusCode: response.status,
      responseTime,
      data: response.data,
      responseStructure: analyzeResponseStructure(response.data),
    };
  } catch (error: any) {
    const responseTime = Date.now() - startTime;
    logTest(testName, "error", {
      error: error.message,
      statusCode: error.response?.status,
      responseTime: `${responseTime}ms`,
    });

    return {
      endpoint: `${ENDPOINT}/suggestions`,
      method: "GET",
      status: "error",
      statusCode: error.response?.status,
      responseTime,
      error: error.message,
      data: error.response?.data,
    };
  }
};

export const testGetRecentProducts = async (): Promise<TestResults> => {
  const testName = "GET Recent Products";
  logTest(testName, "start");

  const startTime = Date.now();
  try {
    const response = await apiClient.get(`${ENDPOINT}/recent-products`, {
      params: {
        perPage: 10,
      },
    });
    const responseTime = Date.now() - startTime;

    logTest(testName, "success", {
      statusCode: response.status,
      dataLength: response.data?.length,
      responseTime: `${responseTime}ms`,
    });

    return {
      endpoint: `${ENDPOINT}/recent-products`,
      method: "GET",
      status: "success",
      statusCode: response.status,
      responseTime,
      data: response.data,
      responseStructure: analyzeResponseStructure(response.data),
    };
  } catch (error: any) {
    const responseTime = Date.now() - startTime;
    logTest(testName, "error", {
      error: error.message,
      statusCode: error.response?.status,
      responseTime: `${responseTime}ms`,
    });

    return {
      endpoint: `${ENDPOINT}/recent-products`,
      method: "GET",
      status: "error",
      statusCode: error.response?.status,
      responseTime,
      error: error.message,
      data: error.response?.data,
    };
  }
};

// Test data for type checking (Updated based on actual API responses)
const testProduct: TAdminShopProduct = {
  id: "prod-123",
  shopifyProductId: "gid://shopify/Product/7890722062390",
  title: "Test Product",
  description: "Test product description",
  createdAt: "2025-07-21T09:43:40.000+00:00",
  updatedAt: "2025-08-07T15:57:51.000+00:00",
  publishedAt: "1969-12-31T19:00:00.000+00:00",
  handle: "test-product",
  status: "active",
  deletedAt: null,
  price: "29.99",
  vendorId: "vendor-123",
  productTypeId: "type-123",
  categoryId: "cat-123",
  pickupOnly: 0,
  fulfilProductId: null,
  classification: null,
  isGift: 0,
  pendingChanges: null,
  pendingApproval: 0,
  variants: [
    {
      id: "var-123",
      productId: "prod-123",
      title: "Test Variant",
      price: "29.99",
      compareAtPrice: "39.99",
      inventoryQuantity: 100,
      inventoryPolicy: "deny",
      sku: "TEST-VAR-123",
      availableForSale: 1,
      numberSold: 0,
      warehouseInventories: [],
    },
  ],
  productType: {
    id: "type-123",
    name: "Test Type",
    createdAt: "2025-01-15T19:45:44.000+00:00",
    updatedAt: "2025-07-21T09:57:38.000+00:00",
    deletedAt: null,
  },
  image: {
    id: "img-123",
    productId: "prod-123",
    shopifyImageId: null,
    src: "https://example.com/image.jpg",
    position: 1,
    createdAt: "2025-07-21T09:57:41.000+00:00",
    updatedAt: "2025-07-21T09:57:41.000+00:00",
    altText: "Test Product Image",
    width: 800,
    height: 600,
    deletedAt: null,
    variantId: null,
  },
  vendor: {
    id: "vendor-123",
    companyName: "Test Vendor",
    createdAt: "2025-01-07T19:42:01.000+00:00",
    updatedAt: "2025-07-24T15:10:45.000+00:00",
    deletedAt: null,
    brandName: null,
    website: null,
    contactName: "Test Contact",
    phone: null,
    email: "<EMAIL>",
    address1: null,
    address2: null,
    city: null,
    state: null,
    country: null,
    zipCode: null,
    ein: null,
    registrationStatus: "approved",
    rejectionReason: null,
    commissionRate: 0.1,
    fixedCommissionAmount: 5,
    warehouseId: "warehouse-123",
    thumbnailId: null,
    description: null,
    returnWarrantyPolicy: null,
    name: "Test Vendor",
  },
  tags: [
    {
      id: "tag-123",
      name: "test",
      createdAt: "2025-01-15T19:46:04.000+00:00",
      updatedAt: "2025-07-21T09:57:38.000+00:00",
      deletedAt: null,
    },
  ],
  reviewSummary: {
    latestReviews: [],
    totalReviews: 0,
    averageRating: 0,
    details: {
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
    },
  },
  onlineStoreUrl: "https://example.com/product/prod-123",
};

const testCategory: TAdminShopCategory = {
  id: "cat-123",
  shopifyId: "gid://shopify/TaxonomyCategory/ae-2-1-2-3-1",
  name: "Test Category",
  level: 6,
  isRoot: 0,
  isLeaf: 1,
  parentId: null,
  ancestorIds: [
    "gid://shopify/TaxonomyCategory/ae-2-1-2-3",
    "gid://shopify/TaxonomyCategory/ae-2-1-2",
    "gid://shopify/TaxonomyCategory/ae-2-1",
    "gid://shopify/TaxonomyCategory/ae-2",
    "gid://shopify/TaxonomyCategory/ae",
  ],
  childrenIds: [],
  fullName:
    "Arts & Entertainment > Hobbies & Creative Arts > Arts & Crafts > Art & Crafting Materials > Craft Paint, Ink & Glaze > Art & Craft Paint",
  isArchived: 0,
  createdAt: "2025-01-01T00:00:00Z",
  updatedAt: "2025-01-01T00:00:00Z",
  deletedAt: null,
  imageId: "img-123",
  image: {
    id: "img-123",
    fileKey: "2025/02/27/test-image.webp",
    url: "https://example.com/test-image.webp",
    createdAt: "2025-02-27T10:45:38.000+00:00",
    updatedAt: "2025-02-27T10:45:38.000+00:00",
    deletedAt: null,
    type: "image",
    resourceId: null,
    sourceFrom: null,
    start: null,
    end: null,
  },
};

const testCollection: TAdminShopCollection = {
  id: "col-123",
  shopifyCollectionId: "gid://shopify/Collection/283112505398",
  title: "Test Collection",
  description: "Test collection description",
  handle: "test-collection",
  imageUrl: "https://example.com/collection.jpg",
  imageAltText: null,
  status: 1,
  createdAt: "2025-01-01T00:00:00Z",
  updatedAt: "2025-01-01T00:00:00Z",
  deletedAt: null,
  imageId: null,
  image: null,
  categories: [testCategory],
  productsCount: 25,
};

// Test utility functions
const testSearchQuery: TAdminShopSearchParams = {
  q: "test product",
  page: 1,
  perPage: 20,
  categoryId: "cat-123",
  sortBy: "relevance",
  sortOrder: "desc",
};

const testCategoryProductsParams: TAdminShopCategoryProductsParams = {
  page: 1,
  perPage: 20,
  sort: "title",
  order: "asc",
  search: "test",
  minPrice: 10,
  maxPrice: 100,
  inStock: true,
};

const testCollectionProductsParams: TAdminShopCollectionProductsParams = {
  page: 1,
  perPage: 20,
  categoryId: "cat-123",
  orderBy: "BEST_SELLING",
  reverse: false,
  search: "test",
  minPrice: 10,
  maxPrice: 100,
  inStock: true,
};

// Test utility function calls
const builtSearchQuery = buildSearchQuery({ q: "test", page: 1 });
const builtCategoryQuery = buildCategoryProductsQuery("cat-123", {
  perPage: 50,
});
const builtCollectionQuery = buildCollectionProductsQuery("col-123", {
  perPage: 50,
});

// Export for testing purposes
export {
  testProduct,
  testCategory,
  testCollection,
  testSearchQuery,
  testCategoryProductsParams,
  testCollectionProductsParams,
  builtSearchQuery,
  builtCategoryQuery,
  builtCollectionQuery,
};

// Type checking function
export function validateTypes(): boolean {
  try {
    // Test product type
    if (typeof testProduct.id !== "string") return false;
    if (typeof testProduct.title !== "string") return false;
    if (testProduct.status !== "active" && testProduct.status !== "draft")
      return false;
    if (typeof testProduct.price !== "string") return false;
    if (!Array.isArray(testProduct.variants)) return false;
    if (typeof testProduct.vendor.companyName !== "string") return false;

    // Test category type
    if (typeof testCategory.id !== "string") return false;
    if (typeof testCategory.name !== "string") return false;
    if (typeof testCategory.level !== "number") return false;
    if (!Array.isArray(testCategory.ancestorIds)) return false;

    // Test collection type
    if (typeof testCollection.id !== "string") return false;
    if (typeof testCollection.title !== "string") return false;
    if (typeof testCollection.status !== "number") return false;
    if (!Array.isArray(testCollection.categories)) return false;

    // Test utility functions
    if (typeof builtSearchQuery.q !== "string") return false;
    if (typeof builtCategoryQuery.categoryId !== "string") return false;
    if (typeof builtCollectionQuery.collectionId !== "string") return false;

    return true;
  } catch (error) {
    console.error("Type validation failed:", error);
    return false;
  }
}

// Service validation function
export function validateService(): boolean {
  try {
    // Check if service has required endpoints
    const endpoints = Object.keys(adminShopService.endpoints);
    const requiredEndpoints = [
      "getCategories",
      "getCategoryProducts",
      "getCollections",
      "getCollectionProducts",
      "searchProducts",
      "getProductDetails",
      "getProductSuggestions",
      "quickAddProducts",
      "getRecentProducts",
      "getPopularProducts",
    ];

    const hasAllEndpoints = requiredEndpoints.every((endpoint) =>
      endpoints.includes(endpoint)
    );

    return hasAllEndpoints;
  } catch (error) {
    console.error("Service validation failed:", error);
    return false;
  }
}

// Main test runner function
export const runAllTests = async (): Promise<{
  summary: {
    total: number;
    passed: number;
    failed: number;
    duration: number;
  };
  results: TestResults[];
}> => {
  console.log("🚀 Starting comprehensive Admin Shop API tests...");
  console.log("JWT Token:", TEST_JWT_TOKEN.substring(0, 50) + "...");

  const startTime = Date.now();
  const results: TestResults[] = [];

  // Test 1: Get Categories
  results.push(await testGetCategories());

  // Test 2: Get Collections
  results.push(await testGetCollections());

  // Test 3: Search Products
  results.push(await testSearchProducts("nail"));

  // Test 4: Get Product Suggestions
  results.push(await testGetProductSuggestions());

  // Test 5: Get Recent Products
  results.push(await testGetRecentProducts());

  // Test 6: Get Category Products (if categories exist)
  const categoriesResult = results.find(
    (r) =>
      r.endpoint.includes("/categories") && !r.endpoint.includes("/products")
  );
  if (
    categoriesResult?.status === "success" &&
    categoriesResult.data?.data?.length > 0
  ) {
    const firstCategoryId = categoriesResult.data.data[0].id;
    results.push(await testGetCategoryProducts(firstCategoryId));
  }

  // Test 7: Get Collection Products (if collections exist)
  const collectionsResult = results.find(
    (r) =>
      r.endpoint.includes("/collections") && !r.endpoint.includes("/products")
  );
  if (
    collectionsResult?.status === "success" &&
    collectionsResult.data?.data?.length > 0
  ) {
    const firstCollectionId = collectionsResult.data.data[0].id;
    results.push(await testGetCollectionProducts(firstCollectionId));
  }

  // Test 8: Get Product Details (if any products found)
  const productsResult = results.find(
    (r) => r.data?.data?.length > 0 || r.data?.products?.length > 0
  );
  if (productsResult?.status === "success") {
    const products = productsResult.data?.data || productsResult.data?.products;
    if (products && products.length > 0) {
      const firstProductId = products[0].id;
      results.push(await testGetProductDetails(firstProductId));
    }
  }

  const endTime = Date.now();
  const duration = endTime - startTime;

  const passed = results.filter((r) => r.status === "success").length;
  const failed = results.filter((r) => r.status === "error").length;

  console.log("\n📊 Test Summary:");
  console.log(`Total tests: ${results.length}`);
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`⏱️ Duration: ${duration}ms`);

  // Log detailed results
  console.log("\n📋 Detailed Results:");
  results.forEach((result, index) => {
    const status = result.status === "success" ? "✅" : "❌";
    console.log(
      `${status} ${index + 1}. ${result.method} ${result.endpoint} (${
        result.statusCode || "N/A"
      }) - ${result.responseTime}ms`
    );
    if (result.error) {
      console.log(`   Error: ${result.error}`);
    }
  });

  return {
    summary: {
      total: results.length,
      passed,
      failed,
      duration,
    },
    results,
  };
};

// Run validation and tests
if (typeof window === "undefined") {
  // Node.js environment
  console.log("Admin Shop Service Validation:");
  console.log("Types valid:", validateTypes());
  console.log("Service valid:", validateService());

  // Uncomment to run tests automatically
  // runAllTests().then(result => {
  //   console.log("Test completed:", result.summary);
  // }).catch(error => {
  //   console.error("Test failed:", error);
  // });
}
