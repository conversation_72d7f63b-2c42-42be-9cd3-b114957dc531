import { apiService } from "./api";

const ADMIN_PRODUCT_ENDPOINT = "v1/admin/product";
const APP_PRODUCT_ENDPOINT = "v1/products";

export const cartProductSelectionService = apiService.injectEndpoints({
  endpoints: (build) => ({
    selectAdminProducts: build.query<TReponsePaging<any>, TQueryAPI>({
      query: (params) => {
        return {
          url: `${ADMIN_PRODUCT_ENDPOINT}/select`,
          method: "GET",
          params,
        };
      },
      transformResponse: (rawResult: TReponsePaging<any>) => {
        const data = rawResult.data?.map((item: any) => ({
          label: item.title,
          value: item.id,
          data: item,
        }));
        return { ...rawResult, data: data || [] };
      },
    }),
    selectAppProducts: build.query<TReponsePaging<any>, TQueryAPI>({
      query: (params) => {
        return {
          url: APP_PRODUCT_ENDPOINT,
          method: "GET",
          params: {
            ...params,
            pageSize: params.limit || 20,
          },
        };
      },
      transformResponse: (rawResult: TReponsePaging<any>) => {
        const data = rawResult.data?.map((item: any) => ({
          label: item.title,
          value: item.id,
          data: item,
        }));
        return { ...rawResult, data: data || [] };
      },
    }),
    getProductForCart: build.query<any, string>({
      query: (productId: string) => {
        return {
          url: `${APP_PRODUCT_ENDPOINT}/${productId}`,
          method: "GET",
        };
      },
      transformResponse: (rawResult: any) => {
        return rawResult;
      },
    }),
    validateProductForCart: build.query<any, string>({
      query: (productId: string) => {
        return {
          url: `${APP_PRODUCT_ENDPOINT}/${productId}`,
          method: "GET",
        };
      },
      transformResponse: (rawResult: any) => {
        return {
          isValid:
            rawResult.status === "active" &&
            !rawResult.isGift &&
            rawResult.variants?.some((v: any) => v.availableForSale),
          availableVariants:
            rawResult.variants?.filter((v: any) => v.availableForSale) || [],
          product: rawResult,
        };
      },
    }),
  }),
  overrideExisting: true,
});

export const {
  useLazySelectAdminProductsQuery,
  useLazySelectAppProductsQuery,
  useLazyGetProductForCartQuery,
  useLazyValidateProductForCartQuery,
} = cartProductSelectionService;
