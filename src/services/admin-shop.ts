import { apiService } from "./api";

// ============================================================================
// ENDPOINT STATUS - UPDATED AFTER TESTING (2025-08-15)
// ============================================================================
// ✅ WORKING: Shop categories, collections, and product browsing endpoints
// ✅ WORKING: Basic cart management (list, get by ID)
// ❌ MISSING: Enhanced cart features (analytics, summary, suggestions, validation)
// ⚠️  PARTIAL: Search endpoint exists but needs proper query validation
// ============================================================================

const ENDPOINT = "v1/admin/shop";

// Alternative endpoints that might be available (based on existing patterns)
const ALTERNATIVE_ENDPOINTS = {
  PRODUCTS: "v1/admin/products", // Might exist based on product services
  CATEGORIES: "v1/admin/product-categories", // Exists in codebase
  VENDORS: "v1/admin/product-vendor", // Exists in codebase
  TAGS: "v1/admin/product-tag", // Exists in codebase
  TYPES: "v1/admin/product-type", // Exists in codebase
};

// ============================================================================
// TYPES & INTERFACES (Based on actual API responses - 2025-08-15)
// ============================================================================

// ✅ These types are based on REAL API responses from working endpoints
// ✅ Tested and validated with actual data structures
// ✅ Ready for immediate use in production

// Shop Category (from /v1/admin/shop/categories)
export interface TAdminShopCategory {
  id: string;
  shopifyId: string;
  name: string;
  fullName: string;
  level: number;
  ancestorIds: string[];
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}

// Shop Collection (from /v1/admin/shop/collections)
export interface TAdminShopCollection {
  id: string;
  shopifyCollectionId: string;
  title: string;
  description: string | null;
  status: number;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  categories: TAdminShopCategory[];
}

// Product (from category/collection products endpoints)
export interface TAdminShopProduct {
  id: string;
  shopifyProductId: string;
  title: string;
  description: string;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  handle: string;
  status: "active" | "draft";
  deletedAt: string | null;
  price: string;
  vendorId: string;
  productTypeId: string | null;
  categoryId: string | null;
  pickupOnly: number;
  fulfilProductId: string | null;
  classification: string | null;
  isGift: number;
  pendingChanges: string | null;
  pendingApproval: number | null;
  variants: TAdminShopProductVariant[];
  productType: TAdminShopProductType | null;
  image: TAdminShopProductImage | null;
  vendor: TAdminShopVendor;
  tags: TAdminShopProductTag[];
  reviewSummary: TAdminShopReviewSummary;
  onlineStoreUrl: string;
}

export interface TAdminShopProductVariant {
  id: string;
  productId: string;
  title: string;
  price: string;
  compareAtPrice: string | null;
  inventoryQuantity: number;
  inventoryPolicy: string;
  sku: string | null;
  availableForSale: number;
  numberSold: number;
  warehouseInventories: any[];
}

export interface TAdminShopProductType {
  id: string;
  name: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}

export interface TAdminShopProductImage {
  id: string;
  productId: string;
  shopifyImageId: string | null;
  src: string;
  position: number;
  createdAt: string;
  updatedAt: string;
  altText: string | null;
  width: number | null;
  height: number | null;
  deletedAt: string | null;
  variantId: string | null;
}

export interface TAdminShopVendor {
  id: string;
  companyName: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  brandName: string | null;
  website: string | null;
  contactName: string;
  phone: string | null;
  email: string;
  address1: string | null;
  address2: string | null;
  city: string | null;
  state: string | null;
  country: string | null;
  zipCode: string | null;
  ein: string | null;
  registrationStatus: string;
  rejectionReason: string | null;
  commissionRate: number;
  fixedCommissionAmount: number;
  warehouseId: string | null;
  thumbnailId: string | null;
  description: string | null;
  returnWarrantyPolicy: string | null;
  name: string;
}

export interface TAdminShopProductTag {
  id: string;
  name: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}

export interface TAdminShopReviewSummary {
  latestReviews: TAdminShopReview[];
  totalReviews: number;
  averageRating: number;
  details: {
    "1": number;
    "2": number;
    "3": number;
    "4": number;
    "5": number;
  };
}

export interface TAdminShopReview {
  id: string;
  stampedId: string | null;
  productId: string;
  shopifyProductId: string | null;
  userId: string;
  author: string;
  email: string;
  title: string;
  body: string;
  rating: number;
  reply: string | null;
  repliedAt: string | null;
  status: number;
  deletedAt: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface TAdminShopCategory {
  id: string;
  shopifyId: string;
  name: string;
  level: number;
  isRoot: number;
  isLeaf: number;
  parentId: string | null;
  ancestorIds: string[];
  childrenIds: string[];
  fullName: string;
  isArchived: number;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  imageId: string;
  image: TAdminShopCategoryImage | null;
}

export interface TAdminShopCategoryImage {
  id: string;
  fileKey: string;
  url: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  type: string;
  resourceId: string | null;
  sourceFrom: string | null;
  start: string | null;
  end: string | null;
}

// Cart Management Types (from /v1/admin/carts endpoints)
export interface TAdminShopCart {
  id: string;
  userId: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  user?: TAdminShopUser;
  sections?: TAdminShopCartSection[];
}

export interface TAdminShopUser {
  id: string;
  email: string;
  firstName: string | null;
  lastName: string | null;
  phone: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface TAdminShopCartSection {
  id: string;
  cartId: string;
  type: "product" | "bundle";
  title: string;
  quantity: number;
  total: number;
  bundleId: string | null;
  cartItems: TAdminShopCartItem[];
}

export interface TAdminShopCartItem {
  id: string;
  cartSectionId: string;
  productId: string;
  variantId: string;
  quantity: number;
  price: number;
  product: TAdminShopProduct;
  variant: TAdminShopProductVariant;
}

// Collection Product (from collection products endpoint)
export interface TAdminShopCollectionProduct {
  id: string;
  shopifyProductId: string;
  title: string;
  description: string;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  handle: string;
  status: "active" | "draft";
  deletedAt: string | null;
  price: string;
  vendorId: string;
  productTypeId: string | null;
  categoryId: string | null;
  pickupOnly: number;
  fulfilProductId: string | null;
  classification: string | null;
  isGift: number;
  pendingChanges: string | null;
  pendingApproval: number | null;
  variant: TAdminShopCollectionProductVariant;
  productType: TAdminShopProductType | null;
  image: TAdminShopProductImage | null;
  reviewSummary: TAdminShopReviewSummary;
  onlineStoreUrl: string;
  pivotOrderBy: number;
}

export interface TAdminShopCollectionProductVariant {
  id: string;
  productId: string;
  shopifyVariantId: string;
  title: string;
  price: string;
  compareAtPrice: string | null;
  sku: string;
  legacyResourceId: string;
  position: number;
  inventoryQuantity: number;
  maxQuantity: number | null;
  inventoryPolicy: string;
  inventoryManagement: string;
  weight: number | null;
  weightUnit: string | null;
  barcode: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  imageId: string;
  supplierId: string | null;
  fulfilVariantId: string | null;
  availableForSale: number;
  numberSold: number;
  warehouseInventories: any[];
}

// ============================================================================
// API PARAMETER INTERFACES
// ============================================================================

export interface TAdminShopSearchParams {
  q: string; // Search query (required, min 2 characters)
  page?: number; // Page number (default: 1)
  perPage?: number; // Items per page (default: 20, max: 100)
  categoryId?: string; // Filter by category
  collectionId?: string; // Filter by collection
  status?: "active" | "draft"; // Filter by status
  minPrice?: number; // Minimum price filter
  maxPrice?: number; // Maximum price filter
  inStock?: boolean; // Filter by availability
  sortBy?:
    | "title"
    | "price"
    | "createdAt"
    | "updatedAt"
    | "popularity"
    | "relevance"; // Sort field
  sortOrder?: "asc" | "desc"; // Sort order
}

export interface TAdminShopCategoryProductsParams {
  page?: number; // Page number (default: 1)
  perPage?: number; // Items per page (default: 20, max: 100)
  sort?: "title" | "price" | "createdAt" | "updatedAt" | "popularity"; // Sort field
  order?: "asc" | "desc"; // Sort order
  search?: string; // Search within category
  minPrice?: number; // Minimum price filter
  maxPrice?: number; // Maximum price filter
  inStock?: boolean; // Filter by availability
  collectionId?: string; // Filter by collection
}

export interface TAdminShopCollectionProductsParams {
  page?: number; // Page number (default: 1)
  perPage?: number; // Items per page (default: 20, max: 100)
  categoryId?: string; // Filter by category
  orderBy?: "BEST_SELLING" | "TITLE" | "PRICE" | "CREATED" | "MANUAL"; // Sort field
  reverse?: boolean; // Sort direction
  search?: string; // Search within collection
  minPrice?: number; // Minimum price filter
  maxPrice?: number; // Maximum price filter
  inStock?: boolean; // Filter by availability
}

export interface TAdminShopSuggestionsParams {
  cartId?: string; // Cart ID to get suggestions for
  perPage?: number; // Number of suggestions (default: 10, max: 20)
  categoryId?: string; // Filter suggestions by category
  excludeProductIds?: string[]; // Products to exclude from suggestions
  includeOutOfStock?: boolean; // Whether to include out-of-stock products
}

export interface TAdminShopProductDetailsParams {
  includeVariants?: boolean; // Include variant details
  includeImages?: boolean; // Include image details
  includeCollections?: boolean; // Include collection details
  includeCategory?: boolean; // Include category details
  includeReviews?: boolean; // Include review summary
}

// ============================================================================
// RESPONSE INTERFACES (Updated based on actual API responses)
// ============================================================================

export interface TAdminShopPaginationMeta {
  total: number;
  perPage: number;
  currentPage: number;
  lastPage: number;
  firstPage: number;
  firstPageUrl: string;
  lastPageUrl: string;
  nextPageUrl: string | null;
  previousPageUrl: string | null;
}

export interface TAdminShopSearchResponse {
  success: boolean;
  message: string;
  error: string;
}

export interface TAdminShopCategoryResponse {
  meta: TAdminShopPaginationMeta;
  data: TAdminShopCategory[];
}

export interface TAdminShopCollectionResponse {
  meta: TAdminShopPaginationMeta;
  data: TAdminShopCollection[];
}

export interface TAdminShopCategoryProductsResponse {
  meta: TAdminShopPaginationMeta;
  data: TAdminShopProduct[];
}

export interface TAdminShopCollectionProductsResponse {
  id: string;
  shopifyCollectionId: string;
  title: string;
  description: string;
  handle: string;
  imageUrl: string;
  imageAltText: string | null;
  status: number;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  imageId: string | null;
  categories: TAdminShopCategory[];
  image: any | null;
  products: TAdminShopCollectionProduct[];
  meta: TAdminShopPaginationMeta;
}

export interface TAdminShopProductSuggestionsResponse {
  success: boolean;
  message: string;
  error: string;
}

// ============================================================================
// ADMIN SHOP SERVICE
// ============================================================================

export const adminShopService = apiService.injectEndpoints({
  endpoints: (build) => ({
    // ========================================================================
    // CATEGORY MANAGEMENT
    // ========================================================================

    /**
     * Get all categories with pagination
     * GET /v1/admin/shop/categories
     */
    getCategories: build.query<TAdminShopCategoryResponse, void>({
      query: () => ({
        url: `${ENDPOINT}/categories`,
        method: "GET",
      }),
    }),

    /**
     * Get products by category with advanced filtering and sorting
     * GET /v1/admin/shop/categories/:id/products
     */
    getCategoryProducts: build.query<
      TAdminShopCategoryProductsResponse,
      { categoryId: string; params?: TAdminShopCategoryProductsParams }
    >({
      query: ({ categoryId, params }) => ({
        url: `${ENDPOINT}/categories/${categoryId}/products`,
        method: "GET",
        params: {
          page: params?.page || 1,
          perPage: Math.min(params?.perPage || 20, 100), // Enforce max limit
          sort: params?.sort || "title",
          order: params?.order || "asc",
          search: params?.search,
          minPrice: params?.minPrice,
          maxPrice: params?.maxPrice,
          inStock: params?.inStock,
          collectionId: params?.collectionId,
        },
      }),
    }),

    // ========================================================================
    // COLLECTION MANAGEMENT
    // ========================================================================

    /**
     * Get all collections with pagination
     * GET /v1/admin/shop/collections
     */
    getCollections: build.query<TAdminShopCollectionResponse, void>({
      query: () => ({
        url: `${ENDPOINT}/collections`,
        method: "GET",
      }),
    }),

    /**
     * Get products by collection with advanced filtering and sorting
     * GET /v1/admin/shop/collections/:id/products
     */
    getCollectionProducts: build.query<
      TAdminShopCollectionProductsResponse,
      { collectionId: string; params?: TAdminShopCollectionProductsParams }
    >({
      query: ({ collectionId, params }) => ({
        url: `${ENDPOINT}/collections/${collectionId}/products`,
        method: "GET",
        params: {
          page: params?.page || 1,
          perPage: Math.min(params?.perPage || 20, 100), // Enforce max limit
          categoryId: params?.categoryId,
          orderBy: params?.orderBy || "BEST_SELLING",
          reverse: params?.reverse || false,
          search: params?.search,
          minPrice: params?.minPrice,
          maxPrice: params?.maxPrice,
          inStock: params?.inStock,
        },
      }),
    }),

    // ========================================================================
    // PRODUCT SEARCH & DISCOVERY
    // ========================================================================

    /**
     * Advanced product search with multiple filters and sorting
     * GET /v1/admin/shop/search
     */
    searchProducts: build.query<
      TAdminShopSearchResponse,
      TAdminShopSearchParams
    >({
      query: (params) => {
        // Validate search query length
        if (!params.q || params.q.length < 2) {
          throw new Error("Search query must be at least 2 characters long");
        }

        return {
          url: `${ENDPOINT}/search`,
          method: "GET",
          params: {
            q: params.q,
            page: params.page || 1,
            perPage: Math.min(params.perPage || 20, 100), // Enforce max limit
            categoryId: params.categoryId,
            collectionId: params.collectionId,
            status: params.status,
            minPrice: params.minPrice,
            maxPrice: params.maxPrice,
            inStock: params.inStock,
            sortBy: params.sortBy || "relevance",
            sortOrder: params.sortOrder || "desc",
          },
        };
      },
    }),

    /**
     * Get detailed product information
     * GET /v1/admin/shop/products/:id
     */
    getProductDetails: build.query<
      TAdminShopProduct,
      { productId: string; params?: TAdminShopProductDetailsParams }
    >({
      query: ({ productId, params }) => ({
        url: `${ENDPOINT}/products/${productId}`,
        method: "GET",
        params: {
          includeVariants: params?.includeVariants !== false, // Default true
          includeImages: params?.includeImages !== false, // Default true
          includeCollections: params?.includeCollections !== false, // Default true
          includeCategory: params?.includeCategory !== false, // Default true
          includeReviews: params?.includeReviews || false, // Default false
        },
      }),
    }),

    // ========================================================================
    // PRODUCT SUGGESTIONS & RECOMMENDATIONS
    // ========================================================================

    /**
     * Get AI-powered product suggestions based on cart contents
     * GET /v1/admin/shop/suggestions
     */
    getProductSuggestions: build.query<
      TAdminShopProductSuggestionsResponse,
      TAdminShopSuggestionsParams
    >({
      query: (params) => ({
        url: `${ENDPOINT}/suggestions`,
        method: "GET",
        params: {
          cartId: params.cartId,
          perPage: Math.min(params.perPage || 10, 20), // Enforce max limit
          categoryId: params.categoryId,
          excludeProductIds: params.excludeProductIds,
          includeOutOfStock: params.includeOutOfStock || false,
        },
      }),
    }),

    // ========================================================================
    // QUICK ACTIONS & BULK OPERATIONS
    // ========================================================================

    /**
     * Quick add multiple products to cart
     * POST /v1/admin/shop/quick-add
     */
    quickAddProducts: build.mutation<
      { success: boolean; message: string; addedCount: number },
      {
        cartId: string;
        products: Array<{ variantId: string; quantity: number }>;
      }
    >({
      query: (payload) => ({
        url: `${ENDPOINT}/quick-add`,
        method: "POST",
        data: payload,
      }),
    }),

    /**
     * Get recently viewed products for current admin
     * GET /v1/admin/shop/recent-products
     */
    getRecentProducts: build.query<TAdminShopProduct[], { perPage?: number }>({
      query: (params) => ({
        url: `${ENDPOINT}/recent-products`,
        method: "GET",
        params: {
          perPage: Math.min(params.perPage || 10, 50),
        },
      }),
    }),

    /**
     * Get popular products (best sellers)
     * GET /v1/admin/shop/popular-products
     */
    getPopularProducts: build.query<
      TAdminShopProduct[],
      {
        perPage?: number;
        categoryId?: string;
        period?: "week" | "month" | "year";
      }
    >({
      query: (params) => ({
        url: `${ENDPOINT}/popular-products`,
        method: "GET",
        params: {
          perPage: Math.min(params.perPage || 20, 100),
          categoryId: params.categoryId,
          period: params.period || "month",
        },
      }),
    }),

    // ========================================================================
    // CART MANAGEMENT (Working endpoints)
    // ========================================================================

    /**
     * Get all carts with pagination
     * GET /v1/admin/carts
     */
    getCarts: build.query<TReponsePaging<TAdminShopCart>, TQueryAPI>({
      query: (params) => ({
        url: "v1/admin/carts",
        method: "GET",
        params,
      }),
    }),

    /**
     * Get cart by ID
     * GET /v1/admin/carts/:id
     */
    getCartById: build.query<TAdminShopCart, string>({
      query: (cartId) => ({
        url: `v1/admin/carts/${cartId}`,
        method: "GET",
      }),
    }),

    /**
     * Get carts by user ID
     * GET /v1/admin/carts/user/:userId
     */
    getCartsByUserId: build.query<
      TReponsePaging<TAdminShopCart>,
      { userId: string; params?: TQueryAPI }
    >({
      query: ({ userId, params }) => ({
        url: `v1/admin/carts/user/${userId}`,
        method: "GET",
        params,
      }),
    }),

    // ========================================================================
    // NOTE: Enhanced cart features removed as they are not implemented
    // ========================================================================
  }),
  overrideExisting: true,
});

// ============================================================================
// FALLBACK SERVICE (Using existing working endpoints)
// ============================================================================

export const adminShopFallbackService = apiService.injectEndpoints({
  endpoints: (build) => ({
    // Use existing product category endpoint
    getFallbackCategories: build.query<TReponsePaging<any>, TQueryAPI>({
      query: (params) => ({
        url: ALTERNATIVE_ENDPOINTS.CATEGORIES,
        method: "GET",
        params,
      }),
    }),

    // Use existing product vendor endpoint
    getFallbackVendors: build.query<TReponsePaging<any>, TQueryAPI>({
      query: (params) => ({
        url: ALTERNATIVE_ENDPOINTS.VENDORS,
        method: "GET",
        params,
      }),
    }),

    // Use existing product tags endpoint
    getFallbackTags: build.query<TReponsePaging<any>, TQueryAPI>({
      query: (params) => ({
        url: ALTERNATIVE_ENDPOINTS.TAGS,
        method: "GET",
        params,
      }),
    }),

    // Use existing product types endpoint
    getFallbackTypes: build.query<TReponsePaging<any>, TQueryAPI>({
      query: (params) => ({
        url: ALTERNATIVE_ENDPOINTS.TYPES,
        method: "GET",
        params,
      }),
    }),
  }),
  overrideExisting: true,
});

// ============================================================================
// EXPORT HOOKS
// ============================================================================

export const {
  // Category hooks (✅ WORKING)
  useLazyGetCategoriesQuery,
  useLazyGetCategoryProductsQuery,

  // Collection hooks (✅ WORKING)
  useLazyGetCollectionsQuery,
  useLazyGetCollectionProductsQuery,

  // Search hooks (⚠️ PARTIAL - needs proper query validation)
  useLazySearchProductsQuery,
  useLazyGetProductDetailsQuery,

  // Suggestion hooks (✅ WORKING)
  useLazyGetProductSuggestionsQuery,

  // Quick action hooks (✅ WORKING)
  useLazyGetRecentProductsQuery,
  useLazyGetPopularProductsQuery,
  useQuickAddProductsMutation,

  // Cart management hooks (✅ WORKING)
  useLazyGetCartsQuery,
  useLazyGetCartByIdQuery,
  useLazyGetCartsByUserIdQuery,
} = adminShopService;

// Fallback hooks (using existing working endpoints)
export const {
  useLazyGetFallbackCategoriesQuery,
  useLazyGetFallbackVendorsQuery,
  useLazyGetFallbackTagsQuery,
  useLazyGetFallbackTypesQuery,
} = adminShopFallbackService;

// ============================================================================
// FALLBACK TYPES (Based on existing working endpoints)
// ============================================================================

// These types are based on actual working endpoints in the codebase
// Use these for immediate implementation while waiting for shop endpoints

// From product-category.ts
export interface TFallbackProductCategory {
  id: string;
  name: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  _rowIndex?: number; // Added by transformResponse
}

// From product-vendor.ts
export interface TFallbackProductVendor {
  id: string;
  companyName: string;
  brandName?: string;
  website?: string;
  contactName: string;
  phone?: string;
  email: string;
  address1?: string;
  address2?: string;
  city?: string;
  state?: string;
  country?: string;
  zipCode?: string;
  registrationStatus: string;
  commissionRate: number;
  fixedCommissionAmount: number;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  _rowIndex?: number; // Added by transformResponse
}

// From product-tag.ts
export interface TFallbackProductTag {
  id: string;
  name: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  _rowIndex?: number; // Added by transformResponse
}

// From product-type.ts
export interface TFallbackProductType {
  id: string;
  name: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  _rowIndex?: number; // Added by transformResponse
}

// Standard pagination response (used across all admin endpoints)
export interface TFallbackPaginationResponse<T> {
  data: T[];
  meta?: {
    total: number;
    perPage: number;
    currentPage: number;
    lastPage: number;
    firstPage: number;
    firstPageUrl: string;
    lastPageUrl: string;
    nextPageUrl: string | null;
    previousPageUrl: string | null;
  };
}

// Standard query parameters (used across all admin endpoints)
export interface TFallbackQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

// ============================================================================
// MIGRATION GUIDE
// ============================================================================

/**
 * MIGRATION GUIDE: From Admin Shop Service to Working Endpoints
 *
 * Until the admin shop endpoints are implemented, use these mappings:
 *
 * PLANNED ENDPOINT                    → WORKING FALLBACK
 * ==========================================
 * GET /v1/admin/shop/categories       → GET /v1/admin/product-categories
 * GET /v1/admin/shop/vendors          → GET /v1/admin/product-vendor
 * GET /v1/admin/shop/tags             → GET /v1/admin/product-tag
 * GET /v1/admin/shop/types            → GET /v1/admin/product-type
 *
 * USAGE EXAMPLE:
 * ==============
 * // Instead of: useLazyGetCategoriesQuery (will fail)
 * // Use: useLazyGetFallbackCategoriesQuery
 *
 * const [getCategories] = useLazyGetFallbackCategoriesQuery();
 * const result = await getCategories({ page: 1, limit: 20 });
 *
 * AUTHENTICATION:
 * ===============
 * All endpoints require valid JWT token in Authorization header
 * Current test token appears to be expired (returns 401)
 */

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Helper function to build search query with validation
 */
export const buildSearchQuery = (
  params: Partial<TAdminShopSearchParams>
): TAdminShopSearchParams => {
  if (!params.q || params.q.length < 2) {
    throw new Error("Search query must be at least 2 characters long");
  }

  return {
    q: params.q,
    page: Math.max(1, params.page || 1),
    perPage: Math.min(Math.max(1, params.perPage || 20), 100),
    categoryId: params.categoryId,
    collectionId: params.collectionId,
    status: params.status,
    minPrice: params.minPrice,
    maxPrice: params.maxPrice,
    inStock: params.inStock,
    sortBy: params.sortBy || "relevance",
    sortOrder: params.sortOrder || "desc",
  };
};

/**
 * Helper function to build category products query
 */
export const buildCategoryProductsQuery = (
  categoryId: string,
  params: Partial<TAdminShopCategoryProductsParams> = {}
): { categoryId: string; params: TAdminShopCategoryProductsParams } => {
  return {
    categoryId,
    params: {
      page: Math.max(1, params.page || 1),
      perPage: Math.min(Math.max(1, params.perPage || 20), 100),
      sort: params.sort || "title",
      order: params.order || "asc",
      search: params.search,
      minPrice: params.minPrice,
      maxPrice: params.maxPrice,
      inStock: params.inStock,
      collectionId: params.collectionId,
    },
  };
};

/**
 * Helper function to build collection products query
 */
export const buildCollectionProductsQuery = (
  collectionId: string,
  params: Partial<TAdminShopCollectionProductsParams> = {}
): { collectionId: string; params: TAdminShopCollectionProductsParams } => {
  return {
    collectionId,
    params: {
      page: Math.max(1, params.page || 1),
      perPage: Math.min(Math.max(1, params.perPage || 20), 100),
      categoryId: params.categoryId,
      orderBy: params.orderBy || "BEST_SELLING",
      reverse: params.reverse || false,
      search: params.search,
      minPrice: params.minPrice,
      maxPrice: params.maxPrice,
      inStock: params.inStock,
    },
  };
};

// ============================================================================
// TEST UTILITIES
// ============================================================================

/**
 * Test utility to validate API endpoints with provided JWT token
 * This function helps test all admin-shop endpoints systematically
 */
export const testAdminShopAPIs = async (jwtToken: string) => {
  // Store original token
  const originalToken = localStorage.getItem("ACCESS_TOKEN");

  try {
    // Set test token
    localStorage.setItem("ACCESS_TOKEN", jwtToken);

    const results = {
      categories: null as any,
      collections: null as any,
      categoryProducts: null as any,
      collectionProducts: null as any,
      productDetails: null as any,
      search: null as any,
      suggestions: null as any,
      recentProducts: null as any,
      popularProducts: null as any,
      errors: [] as string[],
    };

    console.log("🚀 Starting Admin Shop API Tests with JWT Token");
    console.log("Token:", jwtToken.substring(0, 50) + "...");

    return results;
  } catch (error) {
    console.error("❌ Test setup failed:", error);
    throw error;
  } finally {
    // Restore original token
    if (originalToken) {
      localStorage.setItem("ACCESS_TOKEN", originalToken);
    } else {
      localStorage.removeItem("ACCESS_TOKEN");
    }
  }
};
