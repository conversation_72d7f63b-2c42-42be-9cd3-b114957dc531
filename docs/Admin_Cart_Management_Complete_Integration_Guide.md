# Admin Cart Management Complete Integration Guide

## Overview

The Admin Cart Management System provides comprehensive tools for administrators to view, manage, and modify user shopping carts. This system integrates with the existing shop infrastructure to provide a seamless product discovery and cart management experience.

## Architecture

### Core Components

1. **Admin Shop Controller** (`admin/controllers/shop/admin_shop_controller.ts`)

   - Product discovery APIs
   - Category and collection browsing
   - Product search and filtering
   - Product suggestions

2. **Admin Cart Controller** (`admin/controllers/cart/admin_cart_controller.ts`)

   - Basic cart operations (view, update, delete)
   - Add individual products and bundles

3. **Enhanced Admin Cart Controller** (`admin/controllers/cart/enhanced_admin_cart_controller.ts`)

   - Advanced cart management
   - Bulk operations
   - Analytics and insights

4. **Services Layer**
   - `AdminShopService`: Product discovery and management
   - `AdminCartService`: Basic cart operations
   - `EnhancedAdminCartService`: Advanced cart features
   - `ShopCartService`: Core cart logic (shared with app-side)

## API Endpoints

### 1. Product Discovery APIs

#### Browse Categories

```http
GET /v1/admin/shop/categories
GET /v1/admin/shop/categories/:id/products
```

**Query Parameters:**

- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20, max: 100)
- `sort`: Sort field (title, price, createdAt, updatedAt)
- `order`: Sort order (asc, desc)
- `search`: Search by product name/SKU

#### Browse Collections

```http
GET /v1/admin/shop/collections
GET /v1/admin/shop/collections/:id/products
```

**Query Parameters:**

- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20, max: 100)
- `categoryId`: Filter by category
- `orderBy`: Sort field (BEST_SELLING, TITLE, PRICE, CREATED)
- `reverse`: Sort direction (true/false)
- `search`: Search by product name/SKU

#### Product Search

```http
GET /v1/admin/shop/search
```

**Query Parameters:**

- `q`: Search query (required, min 2 characters)
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20, max: 100)
- `categoryId`: Filter by category
- `collectionId`: Filter by collection
- `status`: Filter by status (active, archived)

#### Product Details

```http
GET /v1/admin/shop/products/:id
```

#### Product Suggestions

```http
GET /v1/admin/shop/suggestions
```

**Query Parameters:**

- `cartId`: Cart ID to get suggestions for
- `limit`: Number of suggestions (default: 10, max: 20)

### 2. Basic Cart Management APIs

#### View Carts

```http
GET /v1/admin/carts
GET /v1/admin/carts/user/:userId
GET /v1/admin/carts/:id
```

#### Add Products to Cart

```http
POST /v1/admin/carts/:cartId/items
POST /v1/admin/carts/:cartId/bundles
```

#### Update Cart

```http
PUT /v1/admin/carts/sections/:cartSectionId/quantity
PUT /v1/admin/carts/items/:cartItemId
```

#### Delete Cart

```http
DELETE /v1/admin/carts/sections/:cartSectionId
DELETE /v1/admin/carts/:id
```

### 3. Enhanced Cart Management APIs

#### Quick Add Multiple Products

```http
POST /v1/admin/carts/:cartId/quick-add
```

**Request Body:**

```json
{
  "products": [
    {
      "variantId": "uuid",
      "quantity": 1,
      "affiliateId": "uuid",
      "notes": "Admin note"
    }
  ]
}
```

#### Bulk Add Products

```http
POST /v1/admin/carts/:cartId/bulk-add
```

**Request Body:**

```json
{
  "categoryId": "uuid",
  "limit": 20,
  "affiliateId": "uuid",
  "notes": "Bulk add note"
}
```

**OR**

```json
{
  "collectionId": "uuid",
  "limit": 20,
  "affiliateId": "uuid",
  "notes": "Bulk add note"
}
```

**OR**

```json
{
  "searchQuery": "product name",
  "limit": 20,
  "affiliateId": "uuid",
  "notes": "Bulk add note"
}
```

#### Move Items Between Sections

```http
PUT /v1/admin/carts/move-item
```

**Request Body:**

```json
{
  "cartId": "uuid",
  "cartItemId": "uuid",
  "targetSectionId": "uuid"
}
```

#### Cart Analytics

```http
GET /v1/admin/carts/:cartId/analytics
```

#### Cart Summary

```http
GET /v1/admin/carts/:cartId/summary
```

#### Product Suggestions

```http
GET /v1/admin/carts/suggestions?cartId=uuid&limit=10
```

#### Cart Integrity Validation

```http
GET /v1/admin/carts/:cartId/validate
```

## Complete Admin Cart Flow

### 1. **Product Discovery Phase**

```
Admin → Browse Categories/Collections → Select Products → View Product Details
```

**Step-by-step:**

1. **Browse Categories**: `GET /v1/admin/shop/categories`
2. **View Category Products**: `GET /v1/admin/shop/categories/:id/products`
3. **Browse Collections**: `GET /v1/admin/shop/collections`
4. **View Collection Products**: `GET /v1/admin/shop/collections/:id/products`
5. **Search Products**: `GET /v1/admin/shop/search?q=product_name`
6. **Get Product Details**: `GET /v1/admin/shop/products/:id`

### 2. **Cart Management Phase**

```
Admin → View User's Cart → Add Products → Manage Cart → Validate Integrity
```

**Step-by-step:**

1. **View User's Cart**: `GET /v1/admin/carts/user/:userId`
2. **Quick Add Products**: `POST /v1/admin/carts/:cartId/quick-add`
3. **Bulk Add from Category**: `POST /v1/admin/carts/:cartId/bulk-add`
4. **View Updated Cart**: `GET /v1/admin/carts/:cartId`
5. **Get Cart Analytics**: `GET /v1/admin/carts/:cartId/analytics`
6. **Validate Cart Integrity**: `GET /v1/admin/carts/:cartId/validate`

### 3. **Advanced Operations**

```
Admin → Move Items → Reorganize Cart → Monitor Performance → Generate Reports
```

**Step-by-step:**

1. **Move Items Between Sections**: `PUT /v1/admin/carts/move-item`
2. **Get Product Suggestions**: `GET /v1/admin/carts/suggestions?cartId=uuid`
3. **Update Quantities**: `PUT /v1/admin/carts/sections/:id/quantity`
4. **Delete Sections**: `DELETE /v1/admin/carts/sections/:id`
5. **Get Cart Summary**: `GET /v1/admin/carts/:cartId/summary`

## Data Models

### Cart Structure

```typescript
interface ZnCart {
  id: string;
  userId: string;
  sections: ZnCartSection[];
  createdAt: DateTime;
  updatedAt: DateTime;
}

interface ZnCartSection {
  id: string;
  cartId: string;
  type: "product" | "bundle";
  title: string;
  quantity: number;
  total: number;
  bundleId?: string;
  cartItems: ZnCartItem[];
}

interface ZnCartItem {
  id: string;
  cartSectionId: string;
  productId: string;
  variantId: string;
  quantity: number;
  price: number;
  product: ZnProduct;
  variant: ZnProductVariant;
}
```

### Product Structure

```typescript
interface ZnProduct {
  id: string;
  title: string;
  status: "active" | "draft";
  categoryId: string;
  variants: ZnProductVariant[];
  image: ZnProductImage;
  category: ZnProductCategory;
  collections: ZnCollection[];
  reviewsSummary: ZnProductReviewSummary;
}

interface ZnProductVariant {
  id: string;
  productId: string;
  title: string;
  price: number;
  compareAtPrice: number;
  inventoryQuantity: number;
  sku: string;
  availableForSale: boolean;
}
```

## Authorization

### Required Permissions

- `ACTION.READ` on `RESOURCE.CART` - View carts and analytics
- `ACTION.CREATE` on `RESOURCE.CART` - Add products to carts
- `ACTION.UPDATE` on `RESOURCE.CART` - Modify cart contents
- `ACTION.DELETE` on `RESOURCE.CART` - Remove cart sections
- `ACTION.READ` on `RESOURCE.PRODUCT` - Browse and search products

### JWT Admin Guard

All endpoints require authentication via `jwt_admin` guard with valid admin user.

## Error Handling

### Standard Error Response

```json
{
  "success": false,
  "message": "Error description",
  "error": "Detailed error message"
}
```

### Common Error Scenarios

1. **Authentication Required**: 401 Unauthorized
2. **Permission Denied**: 403 Forbidden
3. **Resource Not Found**: 404 Not Found
4. **Validation Error**: 400 Bad Request
5. **Server Error**: 500 Internal Server Error

## Performance Considerations

### Pagination

- All list endpoints support pagination
- Default page size: 20 items
- Maximum page size: 100 items
- Use `page` and `limit` parameters

### Caching

- Product data is cached at service level
- Cart data is real-time (no caching)
- Use appropriate cache headers for static data

### Database Optimization

- Efficient queries with proper indexing
- Lazy loading for related data
- Batch operations for bulk actions

## Security Features

### Input Validation

- All inputs validated using Vine.js
- SQL injection prevention via ORM
- XSS protection via output encoding

### Access Control

- Role-based permissions via Bouncer
- JWT token validation
- Admin-only endpoint access

### Audit Logging

- All cart modifications logged
- Admin user tracking
- Timestamp and context preservation

## Integration Examples

### Frontend Integration

#### 1. Product Browser Component

```typescript
// Browse categories and products
const browseProducts = async (categoryId: string) => {
  const response = await fetch(
    `/v1/admin/shop/categories/${categoryId}/products`
  );
  const products = await response.json();
  return products;
};
```

#### 2. Cart Management Component

```typescript
// Quick add multiple products
const quickAddProducts = async (cartId: string, products: Product[]) => {
  const response = await fetch(`/v1/admin/carts/${cartId}/quick-add`, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ products }),
  });
  return await response.json();
};
```

#### 3. Cart Analytics Dashboard

```typescript
// Get cart analytics
const getCartAnalytics = async (cartId: string) => {
  const response = await fetch(`/v1/admin/carts/${cartId}/analytics`);
  const analytics = await response.json();
  return analytics;
};
```

### Backend Integration

#### 1. Service Layer Usage

```typescript
import { AdminShopService } from "#adminServices/shop/admin_shop_service";
import { EnhancedAdminCartService } from "#adminServices/cart/enhanced_admin_cart_service";

export class AdminCartManager {
  private shopService = new AdminShopService();
  private cartService = new EnhancedAdminCartService();

  async addProductsFromCategory(cartId: string, categoryId: string) {
    return await this.cartService.bulkAddProducts({
      cartId,
      categoryId,
      limit: 20,
    });
  }
}
```

#### 2. Event Handling

```typescript
// Listen for cart modifications
Event.on("cart:item:added", async (cartId: string, item: CartItem) => {
  // Update analytics
  // Send notifications
  // Log activity
});
```

## Testing

### Unit Tests

- Service layer testing
- Controller method testing
- Validator testing

### Integration Tests

- API endpoint testing
- Database integration testing
- Authorization testing

### Test Data

- Mock products and variants
- Sample cart data
- Test user accounts

## Deployment

### Environment Variables

```bash
# Database
DB_CONNECTION=mysql
DB_HOST=localhost
DB_PORT=3306
DB_USER=zurno
DB_PASSWORD=password
DB_DATABASE=zurno_db

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379

# JWT
JWT_SECRET=your-secret-key
```

### Database Migrations

```bash
# Run migrations
node ace migration:run

# Seed test data
node ace db:seed
```

### Health Checks

```http
GET /health
GET /v1/admin/carts/health
```

## Monitoring and Logging

### Log Levels

- `info`: Normal operations
- `warn`: Potential issues
- `error`: Errors and failures
- `debug`: Detailed debugging info

### Metrics

- API response times
- Cart operation success rates
- Product search performance
- Database query performance

### Alerts

- High error rates
- Slow response times
- Database connection issues
- Memory usage warnings

## Troubleshooting

### Common Issues

#### 1. Product Not Found

- Check if product exists in database
- Verify product status is not 'draft'
- Ensure product has valid variants

#### 2. Cart Section Issues

- Validate cart section exists
- Check section type compatibility
- Verify cart ownership

#### 3. Permission Errors

- Confirm admin user has required permissions
- Check JWT token validity
- Verify resource access rights

### Debug Mode

```bash
# Enable debug logging
NODE_ENV=development
LOG_LEVEL=debug
```

## Future Enhancements

### Planned Features

1. **AI-Powered Product Recommendations**
2. **Advanced Cart Analytics Dashboard**
3. **Bulk Cart Operations**
4. **Cart Templates and Presets**
5. **Real-time Cart Synchronization**

### Performance Improvements

1. **GraphQL API for Complex Queries**
2. **Redis Caching for Product Data**
3. **Database Query Optimization**
4. **CDN Integration for Images**

## Support and Maintenance

### Documentation Updates

- Keep API documentation current
- Update integration examples
- Maintain troubleshooting guides

### Version Management

- Semantic versioning for APIs
- Backward compatibility maintenance
- Deprecation notices

### Community Support

- GitHub issues tracking
- Developer documentation
- Code examples repository

---

This guide provides a comprehensive overview of the Admin Cart Management System. For specific implementation details, refer to the individual component documentation and source code.
